# Exie AI Environment Variables
# Copy this file to .env.local and fill in your API keys

# Daily.co API Key (for video meetings)
# Get your API key from: https://dashboard.daily.co/developers
DAILY_API_KEY=your_daily_api_key_here

# ElevenLabs API Key (for text-to-speech)
# Get your API key from: https://elevenlabs.io/
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# D-ID API Key (for AI video generation)
# Get your API key from: https://www.d-id.com/
D_ID_API_KEY=your_d_id_api_key_here

# OpenAI API Key (for GPT responses)
# Get your API key from: https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here

# Note: The application will work with demo/mock responses if API keys are not provided
# This allows you to test the UI and functionality before setting up all integrations
