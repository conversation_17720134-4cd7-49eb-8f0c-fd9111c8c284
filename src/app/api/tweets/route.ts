import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const { handle } = await req.json();
  const apiKey = process.env.X_API_KEY;
  const apiSecret = process.env.X_API_SECRET_KEY;

  // Twitter/X API v2 bearer token (you may need to generate this with your keys)
  const bearerToken = apiKey;

  try {
    const response = await fetch(`https://api.twitter.com/2/tweets/search/recent?query=from:${handle}&max_results=5&tweet.fields=text`, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
      }
    });
    if (!response.ok) throw new Error('Failed to fetch tweets');
    const data = await response.json();
    const tweets = data.data ? data.data.map((t: any) => t.text) : [];
    return NextResponse.json({ tweets });
  } catch (err) {
    return NextResponse.json({ error: 'Error contacting X (Twitter) API.' }, { status: 500 });
  }
} 