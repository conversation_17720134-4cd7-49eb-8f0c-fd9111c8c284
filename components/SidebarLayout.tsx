import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  const menuItems = [
    {
      href: '/',
      label: 'Home',
      icon: '🏠',
      description: 'Welcome & Overview'
    },
    {
      href: '/dashboard',
      label: 'Dashboard',
      icon: '📊',
      description: 'Analytics & Insights'
    },
    {
      href: '/tweet-center',
      label: 'Tweet Center',
      icon: '🐦',
      description: 'Social Media Hub'
    },
    {
      href: '/meeting',
      label: 'AI Meeting',
      icon: '🎥',
      description: 'Video Calls & AI'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* Modern Sidebar */}
      <aside style={{
        width: '280px',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
        borderRight: '1px solid #e9ecef',
        minHeight: '100vh',
        position: 'relative',
        boxShadow: 'inset -1px 0 0 rgba(0,0,0,0.05)',
      }}>
        {/* Brand Header */}
        <div style={{
          padding: '32px 24px 24px 24px',
          borderBottom: '1px solid #f1f3f4',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: '-50%',
            right: '-50%',
            width: '200%',
            height: '200%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
            pointerEvents: 'none'
          }} />
          <h1 style={{
            margin: 0,
            fontSize: '24px',
            fontWeight: '700',
            letterSpacing: '-0.5px',
            position: 'relative',
            zIndex: 1
          }}>
            Exie AI
          </h1>
          <p style={{
            margin: '4px 0 0 0',
            fontSize: '14px',
            opacity: 0.9,
            fontWeight: '400',
            position: 'relative',
            zIndex: 1
          }}>
            Your AI Assistant
          </p>
        </div>

        {/* Navigation */}
        <nav style={{ padding: '24px 16px' }}>
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            {menuItems.map((item) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <li key={item.href} style={{ marginBottom: '8px' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '16px 20px',
                      borderRadius: '12px',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      cursor: 'pointer',
                      position: 'relative',
                      backgroundColor: active
                        ? 'rgba(102, 126, 234, 0.1)'
                        : hovered
                          ? 'rgba(0, 0, 0, 0.04)'
                          : 'transparent',
                      border: active
                        ? '1px solid rgba(102, 126, 234, 0.2)'
                        : '1px solid transparent',
                      boxShadow: active
                        ? 'inset 0 1px 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0,0,0,0.05)'
                        : hovered
                          ? 'inset 0 1px 2px rgba(0,0,0,0.05), 0 1px 3px rgba(0,0,0,0.05)'
                          : 'none',
                      transform: hovered ? 'translateX(4px)' : 'translateX(0)',
                    }}>
                      {/* Active indicator */}
                      {active && (
                        <div style={{
                          position: 'absolute',
                          left: '0',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '4px',
                          height: '24px',
                          backgroundColor: '#667eea',
                          borderRadius: '0 2px 2px 0',
                        }} />
                      )}

                      {/* Icon */}
                      <span style={{
                        fontSize: '20px',
                        marginRight: '16px',
                        transition: 'transform 0.2s ease',
                        transform: hovered ? 'scale(1.1)' : 'scale(1)',
                      }}>
                        {item.icon}
                      </span>

                      {/* Content */}
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: active ? '600' : '500',
                          color: active ? '#667eea' : '#2c3e50',
                          marginBottom: '2px',
                          transition: 'color 0.2s ease'
                        }}>
                          {item.label}
                        </div>
                        <div style={{
                          fontSize: '12px',
                          color: '#6c757d',
                          opacity: hovered || active ? 1 : 0.7,
                          transition: 'opacity 0.2s ease'
                        }}>
                          {item.description}
                        </div>
                      </div>

                      {/* Hover arrow */}
                      {hovered && (
                        <span style={{
                          fontSize: '14px',
                          color: '#667eea',
                          opacity: 0.7,
                          transition: 'all 0.2s ease'
                        }}>
                          →
                        </span>
                      )}
                    </div>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom section */}
        <div style={{
          position: 'absolute',
          bottom: '24px',
          left: '16px',
          right: '16px',
          padding: '16px',
          backgroundColor: 'rgba(102, 126, 234, 0.05)',
          borderRadius: '12px',
          border: '1px solid rgba(102, 126, 234, 0.1)',
          boxShadow: 'inset 0 1px 2px rgba(102, 126, 234, 0.05)'
        }}>
          <div style={{
            fontSize: '14px',
            fontWeight: '600',
            color: '#667eea',
            marginBottom: '4px'
          }}>
            💡 Pro Tip
          </div>
          <div style={{
            fontSize: '12px',
            color: '#6c757d',
            lineHeight: '1.4'
          }}>
            Use AI Meeting for personalized mentoring sessions
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main style={{
        flexGrow: 1,
        backgroundColor: '#f8f9fa',
        minHeight: '100vh',
        position: 'relative'
      }}>
        {children}
      </main>
    </div>
  );
};

export default SidebarLayout;