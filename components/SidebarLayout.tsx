import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { Home, BarChart3, MessageCircle, Video, ChevronLeft, ChevronRight } from 'lucide-react';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Warm Intelligence Theme - Focused, Intelligent, Warm
  const colors = {
    primary: '#FF6B35',        // Warm coral-orange
    primaryLight: '#FF8A65',   // Soft warm orange
    primaryDark: '#E65100',    // Deep warm orange
    accent: '#FFF3E0',         // Warm cream
    surface: '#FEFEFE',        // Warm white
    surfaceElevated: '#FFFFFF', // Pure white
    background: '#FFF8F3',     // Warm background
    warmGlow: '#FFE0B2',       // Subtle warm glow
    text: {
      primary: '#2D1B14',      // Warm dark brown
      secondary: '#5D4037',    // Medium warm brown
      tertiary: '#8D6E63',     // Light warm brown
      muted: '#BCAAA4',        // Warm gray
      inverse: '#FFFFFF'       // White text
    },
    border: {
      light: '#F5E6D3',        // Warm light border
      medium: '#E1C4A0',       // Warm medium border
      primary: '#FF6B35'       // Primary border
    },
    sidebar: {
      background: 'linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)',
      backgroundSolid: '#FF6B35',
      text: '#FFFFFF',
      textSecondary: 'rgba(255, 255, 255, 0.9)',
      textTertiary: 'rgba(255, 255, 255, 0.7)',
      hover: 'rgba(255, 255, 255, 0.15)',
      active: 'rgba(255, 255, 255, 0.25)',
      border: 'rgba(255, 255, 255, 0.2)',
      glow: 'rgba(255, 107, 53, 0.3)'
    }
  };

  const menuItems = [
    {
      href: '/',
      label: 'Briefing Room',
      icon: Home,
      description: 'Mission Control'
    },
    {
      href: '/tweet-center',
      label: 'Drafting Desk',
      icon: MessageCircle,
      description: 'AI Writing'
    },
    {
      href: '/dashboard',
      label: 'Growth Lab',
      icon: BarChart3,
      description: 'Analytics'
    },
    {
      href: '/meeting',
      label: 'AI Meetings',
      icon: Video,
      description: 'Video Calls'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: '100vh',
      background: `
        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),
        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)
      `,
      padding: '20px',
      gap: '20px'
    }}>
      {/* Intelligent Warm Sidebar */}
      <aside style={{
        width: isCollapsed ? '80px' : '240px',
        background: colors.sidebar.background,
        minHeight: 'calc(100vh - 40px)',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '20px',
        boxShadow: `
          0 20px 60px ${colors.sidebar.glow},
          0 8px 32px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.2)
        `,
        overflow: 'hidden',
        backdropFilter: 'blur(10px)',
        border: `1px solid rgba(255, 255, 255, 0.1)`,
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      }}>
        {/* Simple Brand Header */}
        <div style={{
          padding: isCollapsed ? '24px 12px' : '32px 24px',
          borderBottom: `1px solid ${colors.sidebar.border}`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
          transition: 'padding 0.3s ease'
        }}>
          {/* Simple elegant cursive E */}
          <span style={{
            fontSize: isCollapsed ? '32px' : '48px',
            color: colors.sidebar.text,
            fontWeight: '400',
            fontFamily: 'Georgia, serif',
            fontStyle: 'italic',
            textShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            letterSpacing: '-2px',
            transition: 'font-size 0.3s ease'
          }}>
            ℰ
          </span>

          {/* Toggle Button */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            style={{
              position: 'absolute',
              right: isCollapsed ? '8px' : '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '24px',
              height: '24px',
              borderRadius: '6px',
              background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)`,
              border: '1px solid rgba(255, 255, 255, 0.2)',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              backdropFilter: 'blur(10px)'
            }}
          >
            {isCollapsed ? (
              <ChevronRight size={14} color={colors.sidebar.text} />
            ) : (
              <ChevronLeft size={14} color={colors.sidebar.text} />
            )}
          </button>
        </div>

        {/* Intelligent Navigation */}
        <nav style={{ flex: 1, padding: '20px 0', overflow: 'auto' }}>
          <div style={{ padding: isCollapsed ? '0 8px' : '0 16px', transition: 'padding 0.3s ease' }}>
            {menuItems.map((item, index) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <div key={item.href} style={{ marginBottom: '8px', position: 'relative' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: isCollapsed ? '12px 8px' : '12px 16px',
                      borderRadius: '16px',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      cursor: 'pointer',
                      position: 'relative',
                      justifyContent: isCollapsed ? 'center' : 'flex-start',
                      background: active
                        ? `
                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)
                        `
                        : hovered
                          ? `
                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)
                          `
                          : 'transparent',
                      backdropFilter: (active || hovered) ? 'blur(10px)' : 'none',
                      border: active
                        ? '1px solid rgba(255, 255, 255, 0.3)'
                        : '1px solid transparent',
                      boxShadow: active
                        ? `
                          0 8px 32px rgba(0, 0, 0, 0.1),
                          inset 0 1px 0 rgba(255, 255, 255, 0.4)
                        `
                        : hovered
                          ? `
                            0 4px 16px rgba(0, 0, 0, 0.05),
                            inset 0 1px 0 rgba(255, 255, 255, 0.2)
                          `
                          : 'none',
                      transform: hovered ? 'translateY(-1px) scale(1.02)' : 'translateY(0) scale(1)',
                    }}>
                      {/* Unique flowing active indicator */}
                      {active && !isCollapsed && (
                        <div style={{
                          position: 'absolute',
                          left: '-2px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '4px',
                          height: '24px',
                          background: `
                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)
                          `,
                          borderRadius: '0 8px 8px 0',
                          boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'
                        }} />
                      )}

                      {/* Intelligent icon container */}
                      <div style={{
                        width: '32px',
                        height: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: isCollapsed ? '0' : '12px',
                        borderRadius: '10px',
                        background: active
                          ? `
                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)
                          `
                          : hovered
                            ? `
                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)
                            `
                            : 'transparent',
                        transition: 'all 0.3s ease',
                        transform: hovered ? 'scale(1.1)' : 'scale(1)'
                      }}>
                        <item.icon
                          size={18}
                          color={colors.sidebar.text}
                          style={{
                            transition: 'all 0.3s ease',
                            filter: active ? 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))' : 'none'
                          }}
                        />
                      </div>

                      {/* Warm typography - hidden when collapsed */}
                      {!isCollapsed && (
                        <div style={{
                          fontSize: '15px',
                          fontWeight: active ? '600' : '500',
                          color: colors.sidebar.text,
                          transition: 'all 0.3s ease',
                          letterSpacing: '-0.2px',
                          textShadow: active ? '0 1px 2px rgba(0, 0, 0, 0.1)' : 'none',
                          opacity: isCollapsed ? 0 : 1,
                          transform: isCollapsed ? 'translateX(-10px)' : 'translateX(0)'
                        }}>
                          {item.label}
                        </div>
                      )}

                      {/* Subtle hover glow */}
                      {hovered && !active && !isCollapsed && (
                        <div style={{
                          position: 'absolute',
                          right: '12px',
                          width: '6px',
                          height: '6px',
                          borderRadius: '50%',
                          background: `
                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)
                          `,
                          boxShadow: '0 0 8px rgba(255, 255, 255, 0.6)'
                        }} />
                      )}
                    </div>
                  </Link>

                  {/* Tooltip for collapsed state */}
                  {isCollapsed && hovered && (
                    <div style={{
                      position: 'absolute',
                      left: '70px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      background: `linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)`,
                      color: 'white',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      whiteSpace: 'nowrap',
                      zIndex: 1000,
                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.3)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      pointerEvents: 'none'
                    }}>
                      {item.label}
                      <div style={{
                        position: 'absolute',
                        left: '-4px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: 0,
                        height: 0,
                        borderTop: '4px solid transparent',
                        borderBottom: '4px solid transparent',
                        borderRight: '4px solid rgba(0, 0, 0, 0.9)'
                      }} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </nav>

        {/* Intelligent Account Section */}
        <div style={{
          padding: isCollapsed ? '20px 8px' : '20px 16px',
          borderTop: `1px solid ${colors.sidebar.border}`,
          marginTop: 'auto',
          background: `
            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)
          `,
          transition: 'padding 0.3s ease'
        }}>
          {/* Warm Account Profile */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: isCollapsed ? '0' : '12px',
            padding: isCollapsed ? '12px 8px' : '12px 16px',
            background: `
              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)
            `,
            borderRadius: '16px',
            cursor: 'pointer',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: `
              0 8px 32px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3)
            `,
            justifyContent: isCollapsed ? 'center' : 'flex-start'
          }}>
            {/* Intelligent Avatar */}
            <div style={{
              width: '40px',
              height: '40px',
              background: `
                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)
              `,
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 4px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4)
              `
            }}>
              <span style={{
                color: colors.sidebar.text,
                fontSize: '16px',
                fontWeight: '600',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
              }}>
                A
              </span>
              {/* Intelligent online indicator */}
              <div style={{
                position: 'absolute',
                bottom: '-2px',
                right: '-2px',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                background: `
                  radial-gradient(circle, #00E676 0%, #00C853 100%)
                `,
                border: '2px solid rgba(255, 255, 255, 0.9)',
                boxShadow: '0 0 8px rgba(0, 230, 118, 0.6)'
              }} />
            </div>

            {/* Warm User Info - hidden when collapsed */}
            {!isCollapsed && (
              <>
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '600',
                    color: colors.sidebar.text,
                    lineHeight: '1.2',
                    marginBottom: '2px',
                    textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                    opacity: isCollapsed ? 0 : 1,
                    transform: isCollapsed ? 'translateX(-10px)' : 'translateX(0)',
                    transition: 'all 0.3s ease'
                  }}>
                    Alex Chen
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: colors.sidebar.textTertiary,
                    lineHeight: '1.2',
                    fontWeight: '500',
                    opacity: isCollapsed ? 0 : 1,
                    transform: isCollapsed ? 'translateX(-10px)' : 'translateX(0)',
                    transition: 'all 0.3s ease'
                  }}>
                    AI Manager
                  </div>
                </div>

                {/* Elegant dropdown indicator */}
                <div style={{
                  width: '20px',
                  height: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '6px',
                  background: `
                    linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)
                  `,
                  transition: 'all 0.3s ease',
                  opacity: isCollapsed ? 0 : 1,
                  transform: isCollapsed ? 'translateX(-10px)' : 'translateX(0)'
                }}>
                  <span style={{
                    fontSize: '12px',
                    color: colors.sidebar.textSecondary,
                    transform: 'rotate(0deg)',
                    transition: 'transform 0.3s ease'
                  }}>
                    ⌄
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      </aside>

      {/* Intelligent Content Canvas */}
      <main style={{
        flexGrow: 1,
        position: 'relative'
      }}>
        <div style={{
          backgroundColor: colors.surfaceElevated,
          borderRadius: '20px',
          minHeight: 'calc(100vh - 40px)',
          boxShadow: `
            0 32px 80px rgba(0, 0, 0, 0.12),
            0 8px 32px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 0 1px rgba(255, 107, 53, 0.1)
          `,
          overflow: 'hidden',
          position: 'relative',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          {/* Subtle warm ambient lighting */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '200px',
            background: `
              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)
            `,
            pointerEvents: 'none'
          }} />

          {/* Content with warm context */}
          <div style={{
            position: 'relative',
            zIndex: 1,
            height: '100%'
          }}>
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

export default SidebarLayout;