import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    'daily-operation': true,
    'manage-staff': false,
    'manage-guests': true
  });

  // Fixoria-inspired color palette
  const colors = {
    primary: '#22C55E',        // Green primary
    primaryLight: '#4ADE80',   // Light green
    primaryDark: '#16A34A',    // Dark green
    accent: '#F0FDF4',         // Very light green
    surface: '#FFFFFF',        // Pure white
    surfaceElevated: '#FAFAFA', // Light gray
    background: '#F8FAFC',     // Very light gray background
    text: {
      primary: '#1F2937',      // Dark gray
      secondary: '#6B7280',    // Medium gray
      tertiary: '#9CA3AF',     // Light gray
      muted: '#D1D5DB',        // Very light gray
      inverse: '#FFFFFF'       // White text
    },
    border: {
      light: '#E5E7EB',        // Light border
      medium: '#D1D5DB',       // Medium border
      primary: '#22C55E'       // Primary colored border
    },
    sidebar: {
      background: '#FFFFFF',
      border: '#E5E7EB',
      hover: '#F9FAFB',
      active: '#F0FDF4'
    }
  };

  const navigationSections = [
    {
      id: 'daily-operation',
      title: 'DAILY OPERATION',
      items: [
        {
          href: '/dashboard',
          label: 'Dashboard',
          icon: '📊',
          isActive: router.pathname === '/dashboard'
        },
        {
          href: '/reservation',
          label: 'Reservation',
          icon: '📅',
          isActive: router.pathname.startsWith('/reservation'),
          hasSubmenu: true
        },
        {
          href: '/room-operation',
          label: 'Room Operation',
          icon: '🏠',
          isActive: router.pathname === '/room-operation'
        }
      ]
    },
    {
      id: 'manage-staff',
      title: 'MANAGE STAFF',
      items: [
        {
          href: '/manage-staff',
          label: 'Manage Staff',
          icon: '👥',
          isActive: router.pathname.startsWith('/manage-staff'),
          hasSubmenu: true
        }
      ]
    },
    {
      id: 'manage-guests',
      title: 'MANAGE GUESTS',
      items: [
        {
          href: '/manage-guests',
          label: 'Manage Guests',
          icon: '👤',
          isActive: router.pathname.startsWith('/manage-guests'),
          hasSubmenu: true,
          subItems: [
            {
              href: '/manage-guests/guests-list',
              label: 'Guests List',
              isActive: router.pathname === '/manage-guests/guests-list' || router.pathname === '/meeting'
            },
            {
              href: '/manage-guests/reviews',
              label: 'Guests Reviews',
              isActive: router.pathname === '/manage-guests/reviews'
            }
          ]
        }
      ]
    }
  ];

  const bottomSections = [
    {
      href: '/promotions',
      label: 'Promotions',
      icon: '🎯',
      isActive: router.pathname === '/promotions'
    }
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', backgroundColor: colors.background }}>
      {/* Fixoria-style Sidebar */}
      <aside style={{
        width: '240px',
        background: colors.sidebar.background,
        borderRight: `1px solid ${colors.sidebar.border}`,
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Brand Header */}
        <div style={{
          padding: '16px 20px',
          borderBottom: `1px solid ${colors.border.light}`,
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          {/* Fixoria Logo */}
          <div style={{
            width: '32px',
            height: '32px',
            background: colors.primary,
            borderRadius: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative'
          }}>
            <span style={{
              color: 'white',
              fontSize: '16px',
              fontWeight: '700'
            }}>
              F
            </span>
          </div>

          <div>
            <h1 style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: '600',
              color: colors.text.primary,
              lineHeight: '1.2'
            }}>
              Fixoria ™
            </h1>
          </div>

          {/* Settings icon */}
          <div style={{
            marginLeft: 'auto',
            width: '20px',
            height: '20px',
            background: colors.surfaceElevated,
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}>
            <span style={{ fontSize: '12px', color: colors.text.tertiary }}>⚙</span>
          </div>
        </div>

        {/* Hotel Selection */}
        <div style={{
          padding: '16px 20px',
          borderBottom: `1px solid ${colors.border.light}`
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            cursor: 'pointer'
          }}>
            <div style={{
              width: '24px',
              height: '24px',
              background: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'white', fontSize: '12px', fontWeight: '600' }}>G</span>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '14px',
                fontWeight: '600',
                color: colors.text.primary,
                lineHeight: '1.2'
              }}>
                Grand Sylhet Hotel
              </div>
              <div style={{
                fontSize: '12px',
                color: colors.text.tertiary,
                lineHeight: '1.2'
              }}>
                3 more hotels
              </div>
            </div>
            <span style={{ fontSize: '12px', color: colors.text.tertiary }}>⌄</span>
          </div>
        </div>

        {/* Hierarchical Navigation */}
        <nav style={{ flex: 1, padding: '0', overflow: 'auto' }}>
          {navigationSections.map((section) => (
            <div key={section.id} style={{ marginBottom: '24px' }}>
              {/* Section Header */}
              <div style={{
                padding: '8px 20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                cursor: 'pointer'
              }}
              onClick={() => toggleSection(section.id)}
              >
                <span style={{
                  fontSize: '11px',
                  fontWeight: '600',
                  color: colors.text.tertiary,
                  letterSpacing: '0.5px',
                  textTransform: 'uppercase'
                }}>
                  {section.title}
                </span>
                <span style={{
                  fontSize: '12px',
                  color: colors.text.tertiary,
                  transform: expandedSections[section.id] ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s ease'
                }}>
                  ⌄
                </span>
              </div>

              {/* Section Items */}
              {expandedSections[section.id] && (
                <div style={{ paddingLeft: '8px', paddingRight: '8px' }}>
                  {section.items.map((item) => (
                    <div key={item.href}>
                      {/* Main Item */}
                      <Link href={item.href} style={{ textDecoration: 'none' }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '8px 12px',
                          margin: '2px 0',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          backgroundColor: item.isActive ? colors.sidebar.active : 'transparent',
                          transition: 'all 0.15s ease'
                        }}
                        onMouseEnter={(e) => {
                          if (!item.isActive) {
                            e.currentTarget.style.backgroundColor = colors.sidebar.hover;
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!item.isActive) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                        >
                          {/* Icon */}
                          <span style={{
                            fontSize: '16px',
                            marginRight: '12px',
                            width: '20px',
                            textAlign: 'center'
                          }}>
                            {item.icon}
                          </span>

                          {/* Label */}
                          <span style={{
                            fontSize: '14px',
                            fontWeight: item.isActive ? '600' : '500',
                            color: item.isActive ? colors.text.primary : colors.text.secondary,
                            flex: 1
                          }}>
                            {item.label}
                          </span>

                          {/* Submenu indicator */}
                          {item.hasSubmenu && (
                            <span style={{
                              fontSize: '12px',
                              color: colors.text.tertiary,
                              transform: (item.subItems && expandedSections[`${section.id}-${item.href}`]) ? 'rotate(180deg)' : 'rotate(0deg)',
                              transition: 'transform 0.2s ease'
                            }}>
                              ⌄
                            </span>
                          )}
                        </div>
                      </Link>

                      {/* Sub Items */}
                      {item.subItems && item.isActive && (
                        <div style={{ paddingLeft: '32px', marginTop: '4px', marginBottom: '8px' }}>
                          {item.subItems.map((subItem) => (
                            <Link key={subItem.href} href={subItem.href} style={{ textDecoration: 'none' }}>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '6px 12px',
                                margin: '1px 0',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                backgroundColor: subItem.isActive ? colors.sidebar.active : 'transparent',
                                borderLeft: subItem.isActive ? `2px solid ${colors.primary}` : '2px solid transparent',
                                transition: 'all 0.15s ease'
                              }}
                              onMouseEnter={(e) => {
                                if (!subItem.isActive) {
                                  e.currentTarget.style.backgroundColor = colors.sidebar.hover;
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!subItem.isActive) {
                                  e.currentTarget.style.backgroundColor = 'transparent';
                                }
                              }}
                              >
                                <span style={{
                                  fontSize: '13px',
                                  fontWeight: subItem.isActive ? '600' : '500',
                                  color: subItem.isActive ? colors.text.primary : colors.text.secondary
                                }}>
                                  {subItem.label}
                                </span>
                              </div>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Bottom Section */}
          <div style={{ padding: '8px' }}>
            {bottomSections.map((item) => (
              <Link key={item.href} href={item.href} style={{ textDecoration: 'none' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 12px',
                  margin: '2px 0',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  backgroundColor: item.isActive ? colors.sidebar.active : 'transparent',
                  transition: 'all 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  if (!item.isActive) {
                    e.currentTarget.style.backgroundColor = colors.sidebar.hover;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
                >
                  <span style={{
                    fontSize: '16px',
                    marginRight: '12px',
                    width: '20px',
                    textAlign: 'center'
                  }}>
                    {item.icon}
                  </span>
                  <span style={{
                    fontSize: '14px',
                    fontWeight: item.isActive ? '600' : '500',
                    color: item.isActive ? colors.text.primary : colors.text.secondary
                  }}>
                    {item.label}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </nav>

        {/* Bottom User Section */}
        <div style={{
          padding: '16px 20px',
          borderTop: `1px solid ${colors.border.light}`,
          marginTop: 'auto'
        }}>
          {/* Additional sections */}
          <div style={{ marginBottom: '16px' }}>
            {/* Accounting section */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '8px 0',
              cursor: 'pointer'
            }}>
              <span style={{
                fontSize: '11px',
                fontWeight: '600',
                color: colors.text.tertiary,
                letterSpacing: '0.5px',
                textTransform: 'uppercase'
              }}>
                ACCOUNTING
              </span>
              <span style={{
                fontSize: '16px',
                color: colors.text.tertiary,
                fontWeight: '600'
              }}>
                +
              </span>
            </div>

            {/* System Options section */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '8px 0',
              cursor: 'pointer'
            }}>
              <span style={{
                fontSize: '11px',
                fontWeight: '600',
                color: colors.text.tertiary,
                letterSpacing: '0.5px',
                textTransform: 'uppercase'
              }}>
                SYSTEM OPTIONS
              </span>
              <span style={{
                fontSize: '16px',
                color: colors.text.tertiary,
                fontWeight: '600'
              }}>
                +
              </span>
            </div>
          </div>

          {/* Notifications */}
          <Link href="/notifications" style={{ textDecoration: 'none' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              margin: '2px 0',
              borderRadius: '6px',
              cursor: 'pointer',
              backgroundColor: 'transparent',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.sidebar.hover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            >
              <span style={{
                fontSize: '16px',
                marginRight: '12px',
                width: '20px',
                textAlign: 'center'
              }}>
                🔔
              </span>
              <span style={{
                fontSize: '14px',
                fontWeight: '500',
                color: colors.text.secondary,
                flex: 1
              }}>
                Notifications
              </span>
              <div style={{
                width: '18px',
                height: '18px',
                borderRadius: '50%',
                background: '#EF4444',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '11px',
                color: 'white',
                fontWeight: '600'
              }}>
                1
              </div>
            </div>
          </Link>

          {/* Support */}
          <Link href="/support" style={{ textDecoration: 'none' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              margin: '2px 0',
              borderRadius: '6px',
              cursor: 'pointer',
              backgroundColor: 'transparent',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.sidebar.hover;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            >
              <span style={{
                fontSize: '16px',
                marginRight: '12px',
                width: '20px',
                textAlign: 'center'
              }}>
                ❓
              </span>
              <span style={{
                fontSize: '14px',
                fontWeight: '500',
                color: colors.text.secondary
              }}>
                Support
              </span>
            </div>
          </Link>

          {/* User Profile */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '12px 8px',
            marginTop: '8px',
            cursor: 'pointer'
          }}>
            <div style={{
              width: '32px',
              height: '32px',
              background: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'white', fontSize: '14px', fontWeight: '600' }}>R</span>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '14px',
                fontWeight: '600',
                color: colors.text.primary,
                lineHeight: '1.2'
              }}>
                Rahat Ali
              </div>
              <div style={{
                fontSize: '12px',
                color: colors.text.tertiary,
                lineHeight: '1.2'
              }}>
                Super Admin
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main style={{
        flexGrow: 1,
        backgroundColor: colors.background,
        minHeight: '100vh',
        position: 'relative'
      }}>
        {children}
      </main>
    </div>
  );
};

export default SidebarLayout;