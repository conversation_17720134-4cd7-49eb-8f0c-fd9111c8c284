import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Premium color palette - Light Sky Blue theme
  const colors = {
    primary: '#0EA5E9',      // Light sky blue
    primaryLight: '#38BDF8',  // Lighter variant
    primaryDark: '#0284C7',   // Darker variant
    accent: '#F0F9FF',        // Very light blue
    surface: '#FFFFFF',       // Pure white
    surfaceElevated: '#FAFBFC', // Slightly off-white
    text: {
      primary: '#0F172A',     // Rich dark blue-gray
      secondary: '#475569',   // Medium gray
      tertiary: '#94A3B8',    // Light gray
      inverse: '#FFFFFF'      // White text
    },
    border: {
      light: '#E2E8F0',      // Light border
      medium: '#CBD5E1',     // Medium border
      primary: '#0EA5E9'     // Primary colored border
    },
    shadow: {
      subtle: 'rgba(15, 23, 42, 0.04)',
      medium: 'rgba(15, 23, 42, 0.08)',
      strong: 'rgba(15, 23, 42, 0.12)',
      primary: 'rgba(14, 165, 233, 0.15)'
    }
  };

  const menuItems = [
    {
      href: '/',
      label: 'Overview',
      icon: '⌂',
      description: 'Dashboard & Analytics'
    },
    {
      href: '/dashboard',
      label: 'Intelligence',
      icon: '◈',
      description: 'AI Insights & Reports'
    },
    {
      href: '/tweet-center',
      label: 'Social Hub',
      icon: '◉',
      description: 'Content & Engagement'
    },
    {
      href: '/meeting',
      label: 'Conferences',
      icon: '◎',
      description: 'Video & Collaboration'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', backgroundColor: colors.surfaceElevated }}>
      {/* Premium Sidebar */}
      <aside style={{
        width: '320px',
        background: colors.surface,
        borderRight: `1px solid ${colors.border.light}`,
        minHeight: '100vh',
        position: 'relative',
        boxShadow: `
          0 0 0 1px ${colors.border.light},
          0 4px 24px ${colors.shadow.subtle},
          0 8px 48px ${colors.shadow.medium}
        `,
        zIndex: 100
      }}>
        {/* Premium Brand Header */}
        <div style={{
          padding: '40px 32px 32px 32px',
          borderBottom: `1px solid ${colors.border.light}`,
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.accent} 100%)`,
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Subtle background pattern */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 20%, ${colors.primary}08 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, ${colors.primaryLight}06 0%, transparent 50%)
            `,
            pointerEvents: 'none'
          }} />

          {/* Logo and brand */}
          <div style={{ position: 'relative', zIndex: 1 }}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '12px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px',
              boxShadow: `
                0 4px 12px ${colors.shadow.primary},
                0 0 0 1px ${colors.border.primary}20
              `
            }}>
              <span style={{
                fontSize: '24px',
                color: colors.text.inverse,
                fontWeight: '600'
              }}>
                E
              </span>
            </div>

            <h1 style={{
              margin: 0,
              fontSize: '28px',
              fontWeight: '700',
              letterSpacing: '-0.8px',
              color: colors.text.primary,
              lineHeight: '1.2'
            }}>
              Exie
            </h1>
            <p style={{
              margin: '4px 0 0 0',
              fontSize: '15px',
              color: colors.text.secondary,
              fontWeight: '500',
              letterSpacing: '0.2px'
            }}>
              Enterprise AI Platform
            </p>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ padding: '24px 16px' }}>
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            {menuItems.map((item) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <li key={item.href} style={{ marginBottom: '8px' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '16px 20px',
                      borderRadius: '12px',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      cursor: 'pointer',
                      position: 'relative',
                      backgroundColor: active
                        ? 'rgba(102, 126, 234, 0.1)'
                        : hovered
                          ? 'rgba(0, 0, 0, 0.04)'
                          : 'transparent',
                      border: active
                        ? '1px solid rgba(102, 126, 234, 0.2)'
                        : '1px solid transparent',
                      boxShadow: active
                        ? 'inset 0 1px 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0,0,0,0.05)'
                        : hovered
                          ? 'inset 0 1px 2px rgba(0,0,0,0.05), 0 1px 3px rgba(0,0,0,0.05)'
                          : 'none',
                      transform: hovered ? 'translateX(4px)' : 'translateX(0)',
                    }}>
                      {/* Active indicator */}
                      {active && (
                        <div style={{
                          position: 'absolute',
                          left: '0',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '4px',
                          height: '24px',
                          backgroundColor: '#667eea',
                          borderRadius: '0 2px 2px 0',
                        }} />
                      )}

                      {/* Icon */}
                      <span style={{
                        fontSize: '20px',
                        marginRight: '16px',
                        transition: 'transform 0.2s ease',
                        transform: hovered ? 'scale(1.1)' : 'scale(1)',
                      }}>
                        {item.icon}
                      </span>

                      {/* Content */}
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: active ? '600' : '500',
                          color: active ? '#667eea' : '#2c3e50',
                          marginBottom: '2px',
                          transition: 'color 0.2s ease'
                        }}>
                          {item.label}
                        </div>
                        <div style={{
                          fontSize: '12px',
                          color: '#6c757d',
                          opacity: hovered || active ? 1 : 0.7,
                          transition: 'opacity 0.2s ease'
                        }}>
                          {item.description}
                        </div>
                      </div>

                      {/* Hover arrow */}
                      {hovered && (
                        <span style={{
                          fontSize: '14px',
                          color: '#667eea',
                          opacity: 0.7,
                          transition: 'all 0.2s ease'
                        }}>
                          →
                        </span>
                      )}
                    </div>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom section */}
        <div style={{
          position: 'absolute',
          bottom: '24px',
          left: '16px',
          right: '16px',
          padding: '16px',
          backgroundColor: 'rgba(102, 126, 234, 0.05)',
          borderRadius: '12px',
          border: '1px solid rgba(102, 126, 234, 0.1)',
          boxShadow: 'inset 0 1px 2px rgba(102, 126, 234, 0.05)'
        }}>
          <div style={{
            fontSize: '14px',
            fontWeight: '600',
            color: '#667eea',
            marginBottom: '4px'
          }}>
            💡 Pro Tip
          </div>
          <div style={{
            fontSize: '12px',
            color: '#6c757d',
            lineHeight: '1.4'
          }}>
            Use AI Meeting for personalized mentoring sessions
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main style={{
        flexGrow: 1,
        backgroundColor: '#f8f9fa',
        minHeight: '100vh',
        position: 'relative'
      }}>
        {children}
      </main>
    </div>
  );
};

export default SidebarLayout;