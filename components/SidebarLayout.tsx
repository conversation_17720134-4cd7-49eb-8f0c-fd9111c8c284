import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Minimal Orange theme - Professional & Clean
  const colors = {
    primary: '#F97316',        // Orange
    primaryLight: '#FB923C',   // Light orange
    primaryDark: '#EA580C',    // Dark orange
    accent: '#FFF7ED',         // Very light orange
    surface: '#FFFFFF',        // Pure white
    surfaceElevated: '#FEFEFE', // Slightly off-white
    background: '#F8F9FA',     // Neutral light background
    text: {
      primary: '#1F2937',      // Dark gray
      secondary: '#6B7280',    // Medium gray
      tertiary: '#9CA3AF',     // Light gray
      muted: '#D1D5DB',        // Very light gray
      inverse: '#FFFFFF'       // White text
    },
    border: {
      light: '#E5E7EB',        // Light border
      medium: '#D1D5DB',       // Medium border
      primary: '#F97316'       // Orange border
    },
    sidebar: {
      background: '#F97316',   // Orange sidebar
      backgroundLight: '#FB923C',
      text: '#FFFFFF',
      textSecondary: 'rgba(255, 255, 255, 0.8)',
      textTertiary: 'rgba(255, 255, 255, 0.6)',
      hover: 'rgba(255, 255, 255, 0.1)',
      active: 'rgba(255, 255, 255, 0.15)',
      border: 'rgba(255, 255, 255, 0.1)'
    }
  };

  const menuItems = [
    {
      href: '/',
      label: 'Overview',
      icon: '⌂',
      description: 'Dashboard'
    },
    {
      href: '/dashboard',
      label: 'Analytics',
      icon: '📊',
      description: 'Insights'
    },
    {
      href: '/tweet-center',
      label: 'Social',
      icon: '💬',
      description: 'Content'
    },
    {
      href: '/meeting',
      label: 'Meetings',
      icon: '📹',
      description: 'Video Calls'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', backgroundColor: colors.background }}>
      {/* Minimal Orange Sidebar */}
      <aside style={{
        width: '220px',
        background: colors.sidebar.background,
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Compact Header */}
        <div style={{
          padding: '16px',
          borderBottom: `1px solid ${colors.sidebar.border}`
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <span style={{
              fontSize: '28px',
              color: colors.sidebar.text,
              fontWeight: '400',
              fontFamily: 'Georgia, serif',
              fontStyle: 'italic'
            }}>
              E
            </span>
          </div>
        </div>

        {/* Minimal Navigation */}
        <nav style={{ flex: 1, padding: '12px 0', overflow: 'auto' }}>
          <div style={{ padding: '0 12px' }}>
            {menuItems.map((item) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <div key={item.href} style={{ marginBottom: '4px' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      backgroundColor: active
                        ? colors.sidebar.active
                        : hovered
                          ? colors.sidebar.hover
                          : 'transparent'
                    }}>
                      {/* Active indicator */}
                      {active && (
                        <div style={{
                          position: 'absolute',
                          left: '0',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '3px',
                          height: '16px',
                          background: colors.sidebar.backgroundLight,
                          borderRadius: '0 2px 2px 0'
                        }} />
                      )}

                      {/* Icon */}
                      <div style={{
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '10px'
                      }}>
                        <span style={{
                          fontSize: '14px',
                          color: active ? colors.sidebar.text : colors.sidebar.textSecondary,
                          transition: 'color 0.2s ease'
                        }}>
                          {item.icon}
                        </span>
                      </div>

                      {/* Label */}
                      <div style={{
                        fontSize: '14px',
                        fontWeight: active ? '600' : '500',
                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,
                        transition: 'all 0.2s ease',
                        letterSpacing: '-0.1px'
                      }}>
                        {item.label}
                      </div>
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
        </nav>

        {/* Compact Account Section */}
        <div style={{
          padding: '12px',
          borderTop: `1px solid ${colors.sidebar.border}`,
          marginTop: 'auto'
        }}>

          {/* Minimal Account Profile */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px',
            backgroundColor: colors.sidebar.hover,
            borderRadius: '8px',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}>
            {/* Avatar */}
            <div style={{
              width: '32px',
              height: '32px',
              background: colors.sidebar.backgroundLight,
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}>
              <span style={{
                color: colors.sidebar.text,
                fontSize: '14px',
                fontWeight: '600'
              }}>
                A
              </span>
              {/* Online indicator */}
              <div style={{
                position: 'absolute',
                bottom: '-1px',
                right: '-1px',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#10B981',
                border: '1px solid white'
              }} />
            </div>

            {/* User Info */}
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '13px',
                fontWeight: '600',
                color: colors.sidebar.text,
                lineHeight: '1.2',
                marginBottom: '1px'
              }}>
                Alex Chen
              </div>
              <div style={{
                fontSize: '11px',
                color: colors.sidebar.textTertiary,
                lineHeight: '1.2'
              }}>
                Manager
              </div>
            </div>

            {/* Dropdown indicator */}
            <div style={{
              width: '16px',
              height: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{
                fontSize: '10px',
                color: colors.sidebar.textSecondary
              }}>
                ⌄
              </span>
            </div>
          </div>
        </div>
      </aside>

      {/* Main Content with Elevated Page Effect */}
      <main style={{
        flexGrow: 1,
        backgroundColor: colors.primary,
        minHeight: '100vh',
        position: 'relative',
        padding: '16px'
      }}>
        <div style={{
          backgroundColor: colors.surface,
          borderRadius: '12px',
          minHeight: 'calc(100vh - 32px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.06)',
          overflow: 'hidden'
        }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default SidebarLayout;