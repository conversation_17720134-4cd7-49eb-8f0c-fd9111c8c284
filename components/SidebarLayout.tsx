import Link from 'next/link';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface SidebarLayoutProps {
  children: React.ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutProps> = ({ children }) => {
  const router = useRouter();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Clean SaaS color palette - Light Sky Blue theme
  const colors = {
    primary: '#0EA5E9',        // Light sky blue
    primaryLight: '#38BDF8',   // Lighter variant
    primaryDark: '#0284C7',    // Darker variant
    accent: '#F0F9FF',         // Very light blue
    surface: '#FFFFFF',        // Pure white
    surfaceElevated: '#FAFBFC', // Slightly off-white
    background: '#F8FAFC',     // Light background
    text: {
      primary: '#0F172A',      // Rich dark blue-gray
      secondary: '#475569',    // Medium gray
      tertiary: '#94A3B8',     // Light gray
      muted: '#CBD5E1',        // Very light gray
      inverse: '#FFFFFF'       // White text
    },
    border: {
      light: '#E2E8F0',        // Light border
      medium: '#CBD5E1',       // Medium border
      primary: '#0EA5E9'       // Primary colored border
    },
    sidebar: {
      background: '#FFFFFF',
      border: '#E2E8F0',
      hover: '#F8FAFC',
      active: '#F0F9FF'
    }
  };

  const menuItems = [
    {
      href: '/',
      label: 'Overview',
      icon: '🏠',
      description: 'Dashboard & Analytics'
    },
    {
      href: '/dashboard',
      label: 'Analytics',
      icon: '📊',
      description: 'Data & Insights'
    },
    {
      href: '/tweet-center',
      label: 'Social Hub',
      icon: '🐦',
      description: 'Content & Engagement'
    },
    {
      href: '/meeting',
      label: 'AI Meetings',
      icon: '🎥',
      description: 'Video & Collaboration'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return router.pathname === '/';
    }
    return router.pathname.startsWith(href);
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh', backgroundColor: colors.background }}>
      {/* Clean SaaS Sidebar */}
      <aside style={{
        width: '280px',
        background: colors.sidebar.background,
        borderRight: `1px solid ${colors.sidebar.border}`,
        minHeight: '100vh',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 0 0 1px rgba(0, 0, 0, 0.02), 0 4px 24px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Premium Brand Header */}
        <div style={{
          padding: '32px 24px 24px 24px',
          borderBottom: `1px solid ${colors.border.light}`,
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.accent} 100%)`,
          position: 'relative'
        }}>
          {/* Subtle background pattern */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 20%, ${colors.primary}06 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, ${colors.primaryLight}04 0%, transparent 50%)
            `,
            pointerEvents: 'none'
          }} />

          {/* Logo and brand */}
          <div style={{ position: 'relative', zIndex: 1 }}>
            <div style={{
              width: '48px',
              height: '48px',
              borderRadius: '16px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px',
              boxShadow: `
                0 8px 24px ${colors.primary}30,
                0 0 0 1px ${colors.primary}20,
                inset 0 1px 0 rgba(255, 255, 255, 0.3)
              `
            }}>
              <span style={{
                fontSize: '24px',
                color: colors.text.inverse,
                fontWeight: '700',
                letterSpacing: '-0.5px'
              }}>
                E
              </span>
            </div>

            <h1 style={{
              margin: 0,
              fontSize: '28px',
              fontWeight: '800',
              letterSpacing: '-1px',
              color: colors.text.primary,
              lineHeight: '1.2',
              marginBottom: '4px'
            }}>
              Exie
            </h1>
            <p style={{
              margin: 0,
              fontSize: '15px',
              color: colors.text.secondary,
              fontWeight: '500',
              letterSpacing: '0.1px'
            }}>
              AI-Powered Platform
            </p>
          </div>
        </div>

        {/* Clean Navigation */}
        <nav style={{ flex: 1, padding: '24px 0', overflow: 'auto' }}>
          <div style={{ padding: '0 24px' }}>
            {menuItems.map((item) => {
              const active = isActive(item.href);
              const hovered = hoveredItem === item.href;

              return (
                <div key={item.href} style={{ marginBottom: '8px' }}>
                  <Link
                    href={item.href}
                    style={{ textDecoration: 'none' }}
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '16px 20px',
                      borderRadius: '12px',
                      transition: 'all 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
                      cursor: 'pointer',
                      position: 'relative',
                      backgroundColor: active
                        ? colors.sidebar.active
                        : hovered
                          ? colors.sidebar.hover
                          : 'transparent',
                      border: active
                        ? `1px solid ${colors.border.primary}30`
                        : `1px solid transparent`,
                      boxShadow: active
                        ? `
                          0 0 0 1px ${colors.border.primary}20,
                          0 4px 16px ${colors.primary}15,
                          inset 0 1px 0 rgba(255, 255, 255, 0.8)
                        `
                        : hovered
                          ? `
                            0 0 0 1px ${colors.border.light},
                            0 2px 8px rgba(0, 0, 0, 0.04),
                            inset 0 1px 0 rgba(255, 255, 255, 0.6)
                          `
                          : 'none',
                      transform: hovered ? 'translateY(-1px)' : 'translateY(0)',
                    }}>
                      {/* Active indicator */}
                      {active && (
                        <div style={{
                          position: 'absolute',
                          left: '0',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          width: '4px',
                          height: '24px',
                          background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                          borderRadius: '0 4px 4px 0',
                          boxShadow: `0 0 8px ${colors.primary}40`
                        }} />
                      )}

                      {/* Icon container */}
                      <div style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '10px',
                        background: active
                          ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`
                          : hovered
                            ? colors.surfaceElevated
                            : colors.surfaceElevated,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '16px',
                        transition: 'all 0.3s ease',
                        boxShadow: active
                          ? `
                            0 4px 12px ${colors.primary}30,
                            inset 0 1px 0 rgba(255, 255, 255, 0.3)
                          `
                          : `
                            0 2px 6px rgba(0, 0, 0, 0.04),
                            inset 0 1px 0 rgba(255, 255, 255, 0.5)
                          `,
                        transform: hovered ? 'scale(1.05)' : 'scale(1)',
                      }}>
                        <span style={{
                          fontSize: '18px',
                          color: active ? colors.text.inverse : colors.text.secondary,
                          transition: 'color 0.2s ease'
                        }}>
                          {item.icon}
                        </span>
                      </div>

                      {/* Content */}
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontSize: '16px',
                          fontWeight: active ? '700' : '600',
                          color: active ? colors.primary : colors.text.primary,
                          marginBottom: '2px',
                          transition: 'all 0.2s ease',
                          letterSpacing: '-0.2px'
                        }}>
                          {item.label}
                        </div>
                        <div style={{
                          fontSize: '13px',
                          color: colors.text.tertiary,
                          opacity: hovered || active ? 1 : 0.8,
                          transition: 'opacity 0.2s ease',
                          fontWeight: '500'
                        }}>
                          {item.description}
                        </div>
                      </div>

                      {/* Hover indicator */}
                      {hovered && !active && (
                        <div style={{
                          width: '20px',
                          height: '20px',
                          borderRadius: '6px',
                          background: `linear-gradient(135deg, ${colors.primary}20 0%, ${colors.primaryLight}20 100%)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.2s ease'
                        }}>
                          <span style={{
                            fontSize: '10px',
                            color: colors.primary,
                            fontWeight: '600'
                          }}>
                            →
                          </span>
                        </div>
                      )}
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
        </nav>

        {/* Account Manager Section */}
        <div style={{
          padding: '24px',
          borderTop: `1px solid ${colors.border.light}`,
          marginTop: 'auto',
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.accent} 100%)`
        }}>
          {/* Quick Actions */}
          <div style={{ marginBottom: '20px' }}>
            <div style={{
              display: 'flex',
              gap: '8px',
              marginBottom: '16px'
            }}>
              {/* Settings */}
              <button style={{
                flex: 1,
                padding: '10px',
                border: `1px solid ${colors.border.light}`,
                borderRadius: '8px',
                backgroundColor: colors.surface,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                color: colors.text.secondary
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.surfaceElevated;
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.surface;
                e.currentTarget.style.transform = 'translateY(0)';
              }}
              >
                ⚙️
              </button>

              {/* Notifications */}
              <button style={{
                flex: 1,
                padding: '10px',
                border: `1px solid ${colors.border.light}`,
                borderRadius: '8px',
                backgroundColor: colors.surface,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                color: colors.text.secondary,
                position: 'relative'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.surfaceElevated;
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.surface;
                e.currentTarget.style.transform = 'translateY(0)';
              }}
              >
                🔔
                <div style={{
                  position: 'absolute',
                  top: '6px',
                  right: '6px',
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: '#EF4444',
                  border: '2px solid white'
                }} />
              </button>

              {/* Help */}
              <button style={{
                flex: 1,
                padding: '10px',
                border: `1px solid ${colors.border.light}`,
                borderRadius: '8px',
                backgroundColor: colors.surface,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                color: colors.text.secondary
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.surfaceElevated;
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.surface;
                e.currentTarget.style.transform = 'translateY(0)';
              }}
              >
                ❓
              </button>
            </div>
          </div>

          {/* Account Manager Profile */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '16px',
            backgroundColor: colors.surface,
            borderRadius: '12px',
            border: `1px solid ${colors.border.light}`,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            boxShadow: `
              0 2px 8px rgba(0, 0, 0, 0.04),
              inset 0 1px 0 rgba(255, 255, 255, 0.8)
            `
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = `
              0 4px 16px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.8)
            `;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = `
              0 2px 8px rgba(0, 0, 0, 0.04),
              inset 0 1px 0 rgba(255, 255, 255, 0.8)
            `;
          }}
          >
            {/* Avatar */}
            <div style={{
              width: '44px',
              height: '44px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: `
                0 4px 12px ${colors.primary}30,
                inset 0 1px 0 rgba(255, 255, 255, 0.3)
              `,
              position: 'relative'
            }}>
              <span style={{
                color: 'white',
                fontSize: '18px',
                fontWeight: '700',
                letterSpacing: '-0.5px'
              }}>
                A
              </span>
              {/* Online indicator */}
              <div style={{
                position: 'absolute',
                bottom: '-2px',
                right: '-2px',
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                background: '#10B981',
                border: '2px solid white',
                boxShadow: '0 0 8px rgba(16, 185, 129, 0.4)'
              }} />
            </div>

            {/* User Info */}
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '16px',
                fontWeight: '700',
                color: colors.text.primary,
                lineHeight: '1.2',
                marginBottom: '2px',
                letterSpacing: '-0.2px'
              }}>
                Alex Chen
              </div>
              <div style={{
                fontSize: '13px',
                color: colors.text.tertiary,
                lineHeight: '1.2',
                fontWeight: '500'
              }}>
                Account Manager
              </div>
            </div>

            {/* Dropdown indicator */}
            <div style={{
              width: '24px',
              height: '24px',
              borderRadius: '6px',
              background: colors.surfaceElevated,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease'
            }}>
              <span style={{
                fontSize: '12px',
                color: colors.text.tertiary,
                fontWeight: '600'
              }}>
                ⌄
              </span>
            </div>
          </div>

          {/* Status */}
          <div style={{
            marginTop: '12px',
            padding: '8px 12px',
            backgroundColor: `${colors.primary}10`,
            borderRadius: '8px',
            border: `1px solid ${colors.primary}20`,
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <div style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: colors.primary,
              boxShadow: `0 0 6px ${colors.primary}60`
            }} />
            <span style={{
              fontSize: '12px',
              fontWeight: '600',
              color: colors.primary,
              letterSpacing: '0.2px'
            }}>
              All systems operational
            </span>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main style={{
        flexGrow: 1,
        backgroundColor: colors.background,
        minHeight: '100vh',
        position: 'relative'
      }}>
        {children}
      </main>
    </div>
  );
};

export default SidebarLayout;