'use client';

import React, { useState } from 'react';
import SidebarLayout from '../../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from '../_app';

const GuestsListPage: NextPageWithLayout = () => {
  const [selectedGuests, setSelectedGuests] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    staff: 'All Staff',
    status: 'All',
    monthly: 'Monthly'
  });

  // Fixoria-inspired color palette
  const colors = {
    primary: '#22C55E',
    primaryLight: '#4ADE80',
    surface: '#FFFFFF',
    background: '#F8FAFC',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF',
      muted: '#D1D5DB'
    },
    border: {
      light: '#E5E7EB',
      medium: '#D1D5DB'
    },
    status: {
      paid: '#22C55E',
      pending: '#F59E0B',
      online: '#3B82F6'
    }
  };

  const guestData = [
    {
      id: '#102112',
      name: '<PERSON><PERSON><PERSON>',
      avatar: '👩',
      source: 'Font Desks',
      date: '20/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703570',
      amount: '$1400.00',
      status: 'Cash - Paid',
      statusType: 'paid'
    },
    {
      id: '#102111',
      name: 'Jessy Mac',
      avatar: '👨',
      source: 'Web Reservation',
      date: '19/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01966703578',
      amount: '$400.00',
      status: 'Cash - Paid',
      statusType: 'paid'
    },
    {
      id: '#102110',
      name: 'Maccullam Brad',
      avatar: '👨',
      source: 'Font Desks',
      date: '18/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01866703570',
      amount: '$9800.00',
      status: 'Pending',
      statusType: 'pending'
    },
    {
      id: '#102109',
      name: 'Jhoney Clark',
      avatar: '👨',
      source: 'Group Reservation',
      date: '17/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01966703570',
      amount: '$2400.00',
      status: 'Online - Paid',
      statusType: 'online'
    },
    {
      id: '#102108',
      name: 'Jhon Brak',
      avatar: '👨',
      source: 'Font Desks',
      date: '16/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703544',
      amount: '$1400.00',
      status: 'Pending',
      statusType: 'pending'
    },
    {
      id: '#102107',
      name: 'Red Alone',
      avatar: '👨',
      source: 'Group Reservation',
      date: '15/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703533',
      amount: '$980.00',
      status: 'Cash - Paid',
      statusType: 'paid'
    },
    {
      id: '#102106',
      name: 'Susie Rayden',
      avatar: '👩',
      source: 'Web Reservation',
      date: '13/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703570',
      amount: '$854.00',
      status: 'Cash - Paid',
      statusType: 'paid'
    },
    {
      id: '#102105',
      name: 'Mark Alone',
      avatar: '👨',
      source: 'Font Desks',
      date: '11/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703599',
      amount: '$187.00',
      status: 'Cash - Paid',
      statusType: 'paid'
    },
    {
      id: '#102104',
      name: 'Mukkaram Bari',
      avatar: '👨',
      source: 'Web Reservation',
      date: '10/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703588',
      amount: '$1870.00',
      status: 'Pending',
      statusType: 'pending'
    },
    {
      id: '#102103',
      name: 'Shaniyar Khan',
      avatar: '👨',
      source: 'Font Desks',
      date: '08/12/2024',
      email: '<EMAIL>',
      mobile: '+88 01766703570',
      amount: '$1900.00',
      status: 'Online - Paid',
      statusType: 'online'
    }
  ];

  const toggleGuestSelection = (guestId: string) => {
    setSelectedGuests(prev =>
      prev.includes(guestId)
        ? prev.filter(id => id !== guestId)
        : [...prev, guestId]
    );
  };

  const toggleSelectAll = () => {
    setSelectedGuests(prev =>
      prev.length === guestData.length ? [] : guestData.map(guest => guest.id)
    );
  };

  const getStatusColor = (statusType: string) => {
    switch (statusType) {
      case 'paid': return colors.status.paid;
      case 'pending': return colors.status.pending;
      case 'online': return colors.status.online;
      default: return colors.text.tertiary;
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: colors.background,
      padding: '24px 32px'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '24px'
      }}>
        {/* Breadcrumb */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '14px',
          color: colors.text.tertiary
        }}>
          <span>🏠</span>
          <span>/</span>
          <span>Manage Guests</span>
          <span>/</span>
          <span style={{ color: colors.text.primary, fontWeight: '600' }}>Guests List</span>
        </div>

        {/* Add New Guests Button */}
        <button style={{
          background: colors.primary,
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          padding: '10px 16px',
          fontSize: '14px',
          fontWeight: '600',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          + Add New Guests
        </button>
      </div>

      {/* Main Content Card */}
      <div style={{
        background: colors.surface,
        borderRadius: '12px',
        border: `1px solid ${colors.border.light}`,
        overflow: 'hidden'
      }}>
        {/* Card Header */}
        <div style={{
          padding: '24px 32px',
          borderBottom: `1px solid ${colors.border.light}`,
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        }}>
          {/* Icon and Title */}
          <div style={{
            width: '48px',
            height: '48px',
            background: colors.background,
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px'
          }}>
            👥
          </div>
          <div>
            <h1 style={{
              margin: 0,
              fontSize: '24px',
              fontWeight: '700',
              color: colors.text.primary,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              Guests List
              <span style={{ fontSize: '16px', color: colors.text.tertiary }}>⭐</span>
            </h1>
            <p style={{
              margin: '4px 0 0 0',
              fontSize: '14px',
              color: colors.text.tertiary
            }}>
              Auto-updates in 2 min
            </p>
          </div>
        </div>

        {/* Filters and Actions */}
        <div style={{
          padding: '20px 32px',
          borderBottom: `1px solid ${colors.border.light}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: '16px'
        }}>
          {/* Filter Dropdowns */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {Object.entries(filters).map(([key, value]) => (
              <select
                key={key}
                value={value}
                onChange={(e) => setFilters(prev => ({ ...prev, [key]: e.target.value }))}
                style={{
                  padding: '8px 12px',
                  border: `1px solid ${colors.border.medium}`,
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: colors.surface,
                  color: colors.text.primary,
                  cursor: 'pointer'
                }}
              >
                <option value={value}>{value}</option>
              </select>
            ))}

            {/* Filter Icon */}
            <button style={{
              padding: '8px',
              border: `1px solid ${colors.border.medium}`,
              borderRadius: '6px',
              backgroundColor: colors.surface,
              cursor: 'pointer',
              fontSize: '16px'
            }}>
              🔍
            </button>
          </div>

          {/* Search and Export */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Search */}
            <div style={{ position: 'relative' }}>
              <input
                type="text"
                placeholder="Search..."
                style={{
                  padding: '8px 12px 8px 36px',
                  border: `1px solid ${colors.border.medium}`,
                  borderRadius: '6px',
                  fontSize: '14px',
                  backgroundColor: colors.surface,
                  width: '200px'
                }}
              />
              <span style={{
                position: 'absolute',
                left: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                fontSize: '14px',
                color: colors.text.tertiary
              }}>
                🔍
              </span>
            </div>

            {/* Export Buttons */}
            <button style={{
              padding: '8px 12px',
              border: `1px solid ${colors.border.medium}`,
              borderRadius: '6px',
              backgroundColor: colors.surface,
              fontSize: '14px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              📄 Export PDF
            </button>
            <button style={{
              padding: '8px 12px',
              border: `1px solid ${colors.border.medium}`,
              borderRadius: '6px',
              backgroundColor: colors.surface,
              fontSize: '14px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              📊 Export Excel
            </button>
          </div>
        </div>

        {/* Table */}
        <div style={{ overflow: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '14px'
          }}>
            {/* Table Header */}
            <thead>
              <tr style={{ backgroundColor: colors.background }}>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`,
                  width: '50px'
                }}>
                  <input
                    type="checkbox"
                    checked={selectedGuests.length === guestData.length}
                    onChange={toggleSelectAll}
                    style={{ cursor: 'pointer' }}
                  />
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Booking No
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Name of Guest
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Source
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Date
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Email
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Mobile Number
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Amount
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'left',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`
                }}>
                  Status
                </th>
                <th style={{
                  padding: '16px 24px',
                  textAlign: 'center',
                  fontWeight: '600',
                  color: colors.text.secondary,
                  borderBottom: `1px solid ${colors.border.light}`,
                  width: '50px'
                }}>

                </th>
              </tr>
            </thead>

            {/* Table Body */}
            <tbody>
              {guestData.map((guest, index) => (
                <tr
                  key={guest.id}
                  style={{
                    backgroundColor: index % 2 === 0 ? colors.surface : colors.background,
                    transition: 'background-color 0.15s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#F9FAFB';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = index % 2 === 0 ? colors.surface : colors.background;
                  }}
                >
                  {/* Checkbox */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`
                  }}>
                    <input
                      type="checkbox"
                      checked={selectedGuests.includes(guest.id)}
                      onChange={() => toggleGuestSelection(guest.id)}
                      style={{ cursor: 'pointer' }}
                    />
                  </td>

                  {/* Booking No */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    fontWeight: '600',
                    color: colors.text.primary
                  }}>
                    {guest.id}
                  </td>

                  {/* Name of Guest */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px'
                    }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '50%',
                        background: colors.primary,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '16px'
                      }}>
                        {guest.avatar}
                      </div>
                      <span style={{
                        fontWeight: '600',
                        color: colors.text.primary
                      }}>
                        {guest.name}
                      </span>
                    </div>
                  </td>

                  {/* Source */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    color: colors.text.secondary
                  }}>
                    {guest.source}
                  </td>

                  {/* Date */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    color: colors.text.secondary
                  }}>
                    {guest.date}
                  </td>

                  {/* Email */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    color: colors.text.secondary
                  }}>
                    {guest.email}
                  </td>

                  {/* Mobile Number */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    color: colors.text.secondary
                  }}>
                    {guest.mobile}
                  </td>

                  {/* Amount */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    fontWeight: '600',
                    color: colors.text.primary
                  }}>
                    {guest.amount}
                  </td>

                  {/* Status */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`
                  }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: '600',
                      backgroundColor: `${getStatusColor(guest.statusType)}20`,
                      color: getStatusColor(guest.statusType)
                    }}>
                      {guest.status}
                    </span>
                  </td>

                  {/* Actions */}
                  <td style={{
                    padding: '16px 24px',
                    borderBottom: `1px solid ${colors.border.light}`,
                    textAlign: 'center'
                  }}>
                    <button style={{
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      fontSize: '16px',
                      color: colors.text.tertiary,
                      padding: '4px'
                    }}>
                      ⋯
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div style={{
          padding: '20px 32px',
          borderTop: `1px solid ${colors.border.light}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <button style={{
            padding: '8px 16px',
            border: `1px solid ${colors.border.medium}`,
            borderRadius: '6px',
            backgroundColor: colors.surface,
            fontSize: '14px',
            cursor: 'pointer',
            color: colors.text.secondary
          }}>
            ← Previous
          </button>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            {[1, 2, 10, 12, 13, 14].map((page, index) => (
              <button
                key={page}
                style={{
                  width: '32px',
                  height: '32px',
                  border: page === 1 ? `1px solid ${colors.primary}` : `1px solid ${colors.border.medium}`,
                  borderRadius: '6px',
                  backgroundColor: page === 1 ? colors.primary : colors.surface,
                  color: page === 1 ? 'white' : colors.text.secondary,
                  fontSize: '14px',
                  cursor: 'pointer',
                  fontWeight: page === 1 ? '600' : '400'
                }}
              >
                {page}
              </button>
            ))}
          </div>

          <button style={{
            padding: '8px 16px',
            border: `1px solid ${colors.border.medium}`,
            borderRadius: '6px',
            backgroundColor: colors.surface,
            fontSize: '14px',
            cursor: 'pointer',
            color: colors.text.secondary
          }}>
            Next →
          </button>
        </div>
      </div>
    </div>
  );
};

GuestsListPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default GuestsListPage;
