// pages/tweet-center.tsx
import React, { useState, useRef, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  const [content, setContent] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [mode, setMode] = useState<'manual' | 'ai'>('manual');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    surface: '#FFFFFF',
    warmGlow: '#FFE0B2'
  };

  // AI prediction simulation
  useEffect(() => {
    if (content.length > 10 && mode === 'ai') {
      const timer = setTimeout(() => {
        // Simulate AI prediction based on content
        if (content.includes('AI')) {
          setAiSuggestion(' can revolutionize your workflow and boost productivity by 10x');
        } else if (content.includes('productivity')) {
          setAiSuggestion(' tips that actually work: 1) Time blocking 2) AI automation 3) Single-tasking');
        } else if (content.includes('building')) {
          setAiSuggestion(' in public is the fastest way to grow your audience and get feedback');
        } else {
          setAiSuggestion(' - here\'s what I learned from my experience');
        }
        setShowSuggestion(true);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestion(false);
    }
  }, [content, mode]);

  const handleTabPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && showSuggestion) {
      e.preventDefault();
      setContent(content + aiSuggestion);
      setShowSuggestion(false);
      setAiSuggestion('');
    }
  };

  return (
    <div style={{ padding: '40px', minHeight: '100vh' }}>
      {/* Drafting Desk Header */}
      <div style={{ marginBottom: '40px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '16px' }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '16px',
            background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: `0 8px 24px ${colors.primary}30`
          }}>
            <span style={{ fontSize: '24px' }}>✍️</span>
          </div>
          <div>
            <h1 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '32px',
              fontWeight: '700',
              letterSpacing: '-1px'
            }}>
              Drafting Desk
            </h1>
            <p style={{
              color: colors.text.secondary,
              fontSize: '16px',
              margin: 0
            }}>
              Your intelligent writing companion
            </p>
          </div>
        </div>

        {/* Mode Toggle */}
        <div style={{
          display: 'flex',
          gap: '8px',
          padding: '4px',
          background: `linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)`,
          borderRadius: '12px',
          border: `1px solid ${colors.border}`,
          backdropFilter: 'blur(10px)',
          width: 'fit-content'
        }}>
          <button
            onClick={() => setMode('manual')}
            style={{
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              background: mode === 'manual'
                ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`
                : 'transparent',
              color: mode === 'manual' ? 'white' : colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            ✏️ Manual
          </button>
          <button
            onClick={() => setMode('ai')}
            style={{
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              background: mode === 'ai'
                ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`
                : 'transparent',
              color: mode === 'ai' ? 'white' : colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
          >
            🤖 AI Assist
          </button>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '300px 1fr 300px',
        gap: '24px',
        height: 'calc(100vh - 200px)'
      }}>
        {/* Left Sidebar - Ideas & Hooks */}
        <div style={{
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.warmGlow}15 100%)`,
          borderRadius: '20px',
          padding: '24px',
          boxShadow: `
            0 12px 40px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.8)
          `,
          border: `1px solid ${colors.border}`,
          overflow: 'auto'
        }}>
          <h3 style={{
            color: colors.text.primary,
            margin: 0,
            fontSize: '18px',
            fontWeight: '600',
            marginBottom: '20px'
          }}>
            💡 Ideas & Hooks
          </h3>

          <div style={{ marginBottom: '24px' }}>
            <h4 style={{
              color: colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              marginBottom: '12px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Saved Ideas
            </h4>
            {[
              'AI productivity workflows',
              'Building in public lessons',
              'Remote work tips',
              'Content creation process'
            ].map((idea, index) => (
              <div key={index} style={{
                padding: '12px',
                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 100%)`,
                borderRadius: '12px',
                marginBottom: '8px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                border: '1px solid rgba(255, 255, 255, 0.5)'
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '14px'
                }}>
                  {idea}
                </span>
              </div>
            ))}
          </div>

          <div>
            <h4 style={{
              color: colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              marginBottom: '12px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Trending Hooks
            </h4>
            {[
              '"Here\'s what I learned..."',
              '"The biggest mistake I see..."',
              '"3 things that changed my..."',
              '"If I started over today..."'
            ].map((hook, index) => (
              <div key={index} style={{
                padding: '12px',
                background: `linear-gradient(135deg, ${colors.primary}10 0%, ${colors.primaryLight}05 100%)`,
                borderRadius: '12px',
                marginBottom: '8px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                border: `1px solid ${colors.primary}20`
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '14px',
                  fontStyle: 'italic'
                }}>
                  {hook}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Center - Writing Interface */}
        <div style={{
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.warmGlow}10 100%)`,
          borderRadius: '20px',
          padding: '32px',
          boxShadow: `
            0 20px 60px rgba(0, 0, 0, 0.08),
            0 8px 32px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.9)
          `,
          border: `1px solid ${colors.border}`,
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Ambient glow */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '100px',
            background: `radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)`,
            pointerEvents: 'none'
          }} />

          <div style={{ position: 'relative', zIndex: 1, height: '100%', display: 'flex', flexDirection: 'column' }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{
                color: colors.text.primary,
                margin: 0,
                fontSize: '20px',
                fontWeight: '600',
                marginBottom: '8px'
              }}>
                {mode === 'ai' ? '🤖 AI-Powered Writing' : '✏️ Manual Writing'}
              </h3>
              {mode === 'ai' && (
                <p style={{
                  color: colors.text.tertiary,
                  fontSize: '14px',
                  margin: 0,
                  fontStyle: 'italic'
                }}>
                  Start typing and press Tab to accept AI suggestions
                </p>
              )}
            </div>

            {/* Writing Area */}
            <div style={{ flex: 1, position: 'relative' }}>
              <textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onKeyDown={handleTabPress}
                placeholder={mode === 'ai' ? 'Start writing and I\'ll help you continue...' : 'What\'s on your mind?'}
                style={{
                  width: '100%',
                  height: '100%',
                  minHeight: '300px',
                  padding: '24px',
                  borderRadius: '16px',
                  background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,
                  backdropFilter: 'blur(10px)',
                  fontSize: '18px',
                  lineHeight: '1.6',
                  color: colors.text.primary,
                  fontFamily: 'Georgia, serif',
                  resize: 'none',
                  outline: 'none',
                  boxShadow: `
                    0 8px 32px rgba(0, 0, 0, 0.04),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8)
                  `,
                  border: `1px solid ${colors.border}`
                }}
              />

              {/* AI Suggestion Overlay */}
              {showSuggestion && mode === 'ai' && (
                <div style={{
                  position: 'absolute',
                  bottom: '32px',
                  left: '32px',
                  right: '32px',
                  padding: '12px 16px',
                  background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                  borderRadius: '12px',
                  color: 'white',
                  fontSize: '14px',
                  boxShadow: `0 8px 24px ${colors.primary}40`,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <span>💡</span>
                  <span style={{ flex: 1, opacity: 0.9 }}>
                    {aiSuggestion}
                  </span>
                  <span style={{
                    padding: '4px 8px',
                    background: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '6px',
                    fontSize: '12px',
                    fontWeight: '600'
                  }}>
                    Tab
                  </span>
                </div>
              )}
            </div>

            {/* Action Bar */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '20px',
              padding: '16px 0'
            }}>
              <div style={{
                color: colors.text.tertiary,
                fontSize: '14px'
              }}>
                {content.length}/280 characters
              </div>

              <div style={{ display: 'flex', gap: '12px' }}>
                <button style={{
                  padding: '12px 20px',
                  background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,
                  color: colors.text.primary,
                  border: `1px solid ${colors.border}`,
                  borderRadius: '12px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  backdropFilter: 'blur(10px)',
                  transition: 'all 0.3s ease'
                }}>
                  💾 Save Draft
                </button>

                <button style={{
                  padding: '12px 20px',
                  background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  boxShadow: `0 8px 24px ${colors.primary}40`,
                  transition: 'all 0.3s ease'
                }}>
                  🚀 Post Tweet
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Mentor Voice */}
        <div style={{
          background: `linear-gradient(135deg, ${colors.surface} 0%, ${colors.warmGlow}15 100%)`,
          borderRadius: '20px',
          padding: '24px',
          boxShadow: `
            0 12px 40px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.8)
          `,
          border: `1px solid ${colors.border}`,
          overflow: 'auto'
        }}>
          <h3 style={{
            color: colors.text.primary,
            margin: 0,
            fontSize: '18px',
            fontWeight: '600',
            marginBottom: '20px'
          }}>
            🧠 Mentor Voice
          </h3>

          {/* Real-time Prompts */}
          <div style={{
            background: `linear-gradient(135deg, ${colors.primary}10 0%, ${colors.primaryLight}05 100%)`,
            borderRadius: '16px',
            padding: '20px',
            marginBottom: '20px',
            border: `1px solid ${colors.primary}20`
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '10px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ fontSize: '16px' }}>🎯</span>
              </div>
              <span style={{
                color: colors.text.primary,
                fontSize: '14px',
                fontWeight: '600'
              }}>
                Real-time Tip
              </span>
            </div>
            <p style={{
              color: colors.text.secondary,
              fontSize: '14px',
              margin: 0,
              lineHeight: '1.5',
              fontStyle: 'italic'
            }}>
              "Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions."
            </p>
          </div>

          {/* Brand Tone Guide */}
          <div style={{ marginBottom: '20px' }}>
            <h4 style={{
              color: colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              marginBottom: '12px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Your Brand Tone
            </h4>
            {[
              { tone: 'Helpful', desc: 'Share actionable insights' },
              { tone: 'Authentic', desc: 'Be genuine and personal' },
              { tone: 'Inspiring', desc: 'Motivate and encourage' }
            ].map((item, index) => (
              <div key={index} style={{
                padding: '12px',
                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 100%)`,
                borderRadius: '12px',
                marginBottom: '8px',
                border: '1px solid rgba(255, 255, 255, 0.5)'
              }}>
                <div style={{
                  color: colors.text.primary,
                  fontSize: '14px',
                  fontWeight: '600',
                  marginBottom: '2px'
                }}>
                  {item.tone}
                </div>
                <div style={{
                  color: colors.text.tertiary,
                  fontSize: '12px'
                }}>
                  {item.desc}
                </div>
              </div>
            ))}
          </div>

          {/* Performance Insights */}
          <div>
            <h4 style={{
              color: colors.text.secondary,
              fontSize: '14px',
              fontWeight: '600',
              marginBottom: '12px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              What's Working
            </h4>
            {[
              'Posts with "AI" get 3x engagement',
              'Questions drive 2x more replies',
              'Behind-the-scenes content performs well'
            ].map((insight, index) => (
              <div key={index} style={{
                padding: '12px',
                background: `linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 200, 83, 0.05) 100%)`,
                borderRadius: '12px',
                marginBottom: '8px',
                border: '1px solid rgba(0, 230, 118, 0.2)'
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '13px'
                }}>
                  📈 {insight}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage;