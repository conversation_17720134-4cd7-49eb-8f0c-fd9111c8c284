// pages/tweet-center.tsx
import React, { useState, useRef, useEffect } from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const TweetCenterPage: NextPageWithLayout = () => {
  const [content, setContent] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [mode, setMode] = useState<'manual' | 'ai'>('manual');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colors = {
    primary: '#FF6B35',
    primaryLight: '#FF8A65',
    text: {
      primary: '#2D1B14',
      secondary: '#5D4037',
      tertiary: '#8D6E63'
    },
    border: '#F5E6D3',
    surface: '#FFFFFF',
    warmGlow: '#FFE0B2'
  };

  // AI prediction simulation
  useEffect(() => {
    if (content.length > 10 && mode === 'ai') {
      const timer = setTimeout(() => {
        // Simulate AI prediction based on content
        if (content.includes('AI')) {
          setAiSuggestion(' can revolutionize your workflow and boost productivity by 10x');
        } else if (content.includes('productivity')) {
          setAiSuggestion(' tips that actually work: 1) Time blocking 2) AI automation 3) Single-tasking');
        } else if (content.includes('building')) {
          setAiSuggestion(' in public is the fastest way to grow your audience and get feedback');
        } else {
          setAiSuggestion(' - here\'s what I learned from my experience');
        }
        setShowSuggestion(true);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestion(false);
    }
  }, [content, mode]);

  const handleTabPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && showSuggestion) {
      e.preventDefault();
      setContent(content + aiSuggestion);
      setShowSuggestion(false);
      setAiSuggestion('');
    }
  };

  return (
    <div style={{ padding: '32px', height: '100vh', overflow: 'auto' }}>
      {/* Clean Header */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '28px',
          fontWeight: '600',
          letterSpacing: '-0.5px',
          marginBottom: '8px'
        }}>
          Drafting Desk
        </h1>
        <p style={{
          color: colors.text.secondary,
          fontSize: '16px',
          margin: 0,
          fontWeight: '400',
          marginBottom: '20px'
        }}>
          Your intelligent writing companion
        </p>

        {/* Clean Mode Toggle */}
        <div style={{
          display: 'flex',
          gap: '4px',
          padding: '4px',
          background: colors.surface,
          borderRadius: '8px',
          border: `1px solid ${colors.border}`,
          width: 'fit-content'
        }}>
          <button
            onClick={() => setMode('manual')}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: 'none',
              background: mode === 'manual' ? colors.primary : 'transparent',
              color: mode === 'manual' ? 'white' : colors.text.secondary,
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            Manual
          </button>
          <button
            onClick={() => setMode('ai')}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: 'none',
              background: mode === 'ai' ? colors.primary : 'transparent',
              color: mode === 'ai' ? 'white' : colors.text.secondary,
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            AI Assist
          </button>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '240px 1fr 240px',
        gap: '20px',
        height: 'calc(100vh - 180px)'
      }}>
        {/* Left Panel - Ideas & Hooks */}
        <div style={{
          background: colors.surface,
          borderRadius: '12px',
          padding: '20px',
          boxShadow: `0 2px 8px rgba(0, 0, 0, 0.04)`,
          border: `1px solid ${colors.border}`,
          overflow: 'auto'
        }}>
          <h3 style={{
            color: colors.text.primary,
            margin: 0,
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '16px'
          }}>
            Ideas & Hooks
          </h3>

          <div style={{ marginBottom: '20px' }}>
            <h4 style={{
              color: colors.text.tertiary,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '8px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Saved Ideas
            </h4>
            {[
              'AI productivity workflows',
              'Building in public lessons',
              'Remote work tips',
              'Content creation process'
            ].map((idea, index) => (
              <div key={index} style={{
                padding: '8px 12px',
                background: `${colors.primary}05`,
                borderRadius: '6px',
                marginBottom: '6px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                border: `1px solid ${colors.primary}10`
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '13px'
                }}>
                  {idea}
                </span>
              </div>
            ))}
          </div>

          <div>
            <h4 style={{
              color: colors.text.tertiary,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '8px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Trending Hooks
            </h4>
            {[
              'Here\'s what I learned...',
              'The biggest mistake I see...',
              '3 things that changed my...',
              'If I started over today...'
            ].map((hook, index) => (
              <div key={index} style={{
                padding: '8px 12px',
                background: `${colors.primary}08`,
                borderRadius: '6px',
                marginBottom: '6px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                border: `1px solid ${colors.primary}15`
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '13px',
                  fontStyle: 'italic'
                }}>
                  "{hook}"
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Center - Paper-like Writing Interface */}
        <div style={{
          background: colors.surface,
          borderRadius: '12px',
          padding: '24px',
          boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,
          border: `1px solid ${colors.border}`,
          position: 'relative',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div style={{ marginBottom: '16px' }}>
            <h3 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '4px'
            }}>
              {mode === 'ai' ? 'AI-Powered Writing' : 'Manual Writing'}
            </h3>
            {mode === 'ai' && (
              <p style={{
                color: colors.text.tertiary,
                fontSize: '13px',
                margin: 0,
                fontWeight: '400'
              }}>
                Start typing and press Tab to accept AI suggestions
              </p>
            )}
          </div>

          {/* Clean Writing Area */}
          <div style={{ flex: 1, position: 'relative' }}>
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyDown={handleTabPress}
              placeholder={mode === 'ai' ? 'Start writing and I\'ll help you continue...' : 'What\'s on your mind?'}
              style={{
                width: '100%',
                height: '100%',
                minHeight: '320px',
                padding: '20px',
                borderRadius: '8px',
                background: '#FEFEFE',
                fontSize: '16px',
                lineHeight: '1.6',
                color: colors.text.primary,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                resize: 'none',
                outline: 'none',
                boxShadow: `inset 0 1px 3px rgba(0, 0, 0, 0.06)`,
                border: `1px solid ${colors.border}`,
                transition: 'border-color 0.2s ease'
              }}
            />

            {/* Clean AI Suggestion */}
            {showSuggestion && mode === 'ai' && (
              <div style={{
                position: 'absolute',
                bottom: '24px',
                left: '24px',
                right: '24px',
                padding: '12px 16px',
                background: colors.primary,
                borderRadius: '8px',
                color: 'white',
                fontSize: '14px',
                boxShadow: `0 4px 12px ${colors.primary}40`,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <span style={{ flex: 1 }}>
                  {aiSuggestion}
                </span>
                <span style={{
                  padding: '2px 6px',
                  background: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: '4px',
                  fontSize: '11px',
                  fontWeight: '600'
                }}>
                  Tab
                </span>
              </div>
            )}
          </div>

          {/* Clean Action Bar */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '16px',
            paddingTop: '16px',
            borderTop: `1px solid ${colors.border}`
          }}>
            <div style={{
              color: colors.text.tertiary,
              fontSize: '13px',
              fontWeight: '500'
            }}>
              {content.length}/280 characters
            </div>

            <div style={{ display: 'flex', gap: '8px' }}>
              <button style={{
                padding: '8px 16px',
                background: colors.surface,
                color: colors.text.primary,
                border: `1px solid ${colors.border}`,
                borderRadius: '6px',
                fontSize: '13px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Save Draft
              </button>

              <button style={{
                padding: '8px 16px',
                background: colors.primary,
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '13px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}>
                Post Tweet
              </button>
            </div>
          </div>
        </div>

        {/* Right Panel - Mentor Voice */}
        <div style={{
          background: colors.surface,
          borderRadius: '12px',
          padding: '20px',
          boxShadow: `0 2px 8px rgba(0, 0, 0, 0.04)`,
          border: `1px solid ${colors.border}`,
          overflow: 'auto'
        }}>
          <h3 style={{
            color: colors.text.primary,
            margin: 0,
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '16px'
          }}>
            AI Mentor
          </h3>

          {/* Real-time Tip */}
          <div style={{
            background: `${colors.primary}08`,
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '16px',
            border: `1px solid ${colors.primary}15`
          }}>
            <div style={{
              color: colors.text.primary,
              fontSize: '13px',
              fontWeight: '600',
              marginBottom: '8px'
            }}>
              Real-time Tip
            </div>
            <p style={{
              color: colors.text.secondary,
              fontSize: '13px',
              margin: 0,
              lineHeight: '1.4'
            }}>
              Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions.
            </p>
          </div>

          {/* Brand Tone Guide */}
          <div style={{ marginBottom: '16px' }}>
            <h4 style={{
              color: colors.text.tertiary,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '8px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Your Brand Tone
            </h4>
            {[
              { tone: 'Helpful', desc: 'Share actionable insights' },
              { tone: 'Authentic', desc: 'Be genuine and personal' },
              { tone: 'Inspiring', desc: 'Motivate and encourage' }
            ].map((item, index) => (
              <div key={index} style={{
                padding: '8px 12px',
                background: `${colors.primary}05`,
                borderRadius: '6px',
                marginBottom: '6px',
                border: `1px solid ${colors.primary}10`
              }}>
                <div style={{
                  color: colors.text.primary,
                  fontSize: '13px',
                  fontWeight: '600',
                  marginBottom: '2px'
                }}>
                  {item.tone}
                </div>
                <div style={{
                  color: colors.text.tertiary,
                  fontSize: '11px'
                }}>
                  {item.desc}
                </div>
              </div>
            ))}
          </div>

          {/* Performance Insights */}
          <div>
            <h4 style={{
              color: colors.text.tertiary,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '8px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              What's Working
            </h4>
            {[
              'Posts with "AI" get 3x engagement',
              'Questions drive 2x more replies',
              'Behind-the-scenes content performs well'
            ].map((insight, index) => (
              <div key={index} style={{
                padding: '8px 12px',
                background: '#F0F9FF',
                borderRadius: '6px',
                marginBottom: '6px',
                border: '1px solid #E0F2FE'
              }}>
                <span style={{
                  color: colors.text.secondary,
                  fontSize: '12px'
                }}>
                  {insight}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

TweetCenterPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default TweetCenterPage;