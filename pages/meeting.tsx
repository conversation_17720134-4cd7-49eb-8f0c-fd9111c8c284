'use client';

import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const MeetingPage: NextPageWithLayout = () => {
  const colors = {
    primary: '#F97316',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    border: '#E5E7EB',
    surface: '#FFFFFF',
    background: '#F8F9FA'
  };

  return (
    <div style={{
      padding: '32px',
      height: '100vh',
      overflow: 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Clean Header */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '28px',
          fontWeight: '600',
          letterSpacing: '-0.5px',
          marginBottom: '8px'
        }}>
          AI Meetings
        </h1>
        <p style={{
          color: colors.text.secondary,
          fontSize: '16px',
          margin: 0,
          fontWeight: '400'
        }}>
          AI-powered mentoring session
        </p>
      </div>

      {/* Clean Video Container */}
      <div style={{
        flex: 1,
        position: 'relative'
      }}>
        <div style={{
          height: '100%',
          backgroundColor: colors.surface,
          borderRadius: '16px',
          overflow: 'hidden',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)',
          border: `1px solid ${colors.border}`,
          position: 'relative'
        }}>
          {/* Meeting Status */}
          <div style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            backgroundColor: '#10B981',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '12px',
            fontWeight: '600',
            zIndex: 10
          }}>
            Live Meeting
          </div>

          <iframe
            src="https://demo.daily.co/hello"
            allow="camera; microphone; fullscreen; display-capture; autoplay"
            style={{
              width: '100%',
              height: '100%',
              border: 'none'
            }}
            title="Daily.co Video Meeting"
          />

          {/* Clean AI Assistant Button */}
          <button
            onClick={() => alert('AI Assistant integration coming soon! This will allow you to talk to Exie during your meeting.')}
            style={{
              position: 'absolute',
              bottom: '20px',
              right: '20px',
              backgroundColor: colors.primary,
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              padding: '12px 16px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.2s ease',
              zIndex: 10
            }}
            title="Talk to Exie AI"
          >
            Talk to AI
          </button>
        </div>
      </div>
    </div>
  );
};

MeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default MeetingPage;
