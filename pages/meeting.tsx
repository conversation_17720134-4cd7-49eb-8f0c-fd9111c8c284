'use client';

import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const MeetingPage: NextPageWithLayout = () => {
  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column', 
      backgroundColor: '#f8f9fa',
      position: 'relative' 
    }}>
      {/* Header with meeting info */}
      <div style={{
        padding: '20px 24px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e9ecef',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 2px 4px rgba(0,0,0,0.04)'
      }}>
        <div>
          <h1 style={{ 
            color: '#2c3e50', 
            margin: 0, 
            fontSize: '24px',
            fontWeight: '600'
          }}>
            Exie AI Meeting
          </h1>
          <p style={{ 
            margin: '4px 0 0 0', 
            fontSize: '14px', 
            color: '#6c757d' 
          }}>
            AI-powered mentoring session
          </p>
        </div>
        <div style={{
          backgroundColor: '#e8f5e8',
          color: '#28a745',
          padding: '8px 16px',
          borderRadius: '20px',
          fontSize: '14px',
          fontWeight: '500'
        }}>
          🟢 Live Meeting
        </div>
      </div>

      {/* Daily.co iframe container */}
      <div style={{
        flex: 1,
        padding: '24px',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <div style={{
          flex: 1,
          backgroundColor: 'white',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
          border: '1px solid #e9ecef'
        }}>
          <iframe
            src="https://demo.daily.co/hello"
            allow="camera; microphone; fullscreen; display-capture; autoplay"
            style={{
              width: '100%',
              height: '100%',
              border: 'none'
            }}
            title="Daily.co Video Meeting"
          />
        </div>
      </div>

      {/* Floating AI Assistant Button */}
      <button
        onClick={() => alert('AI Assistant integration coming soon! This will allow you to talk to Exie during your meeting.')}
        style={{
          position: 'absolute',
          bottom: '32px',
          right: '32px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '64px',
          height: '64px',
          fontSize: '28px',
          cursor: 'pointer',
          boxShadow: '0 8px 24px rgba(0, 123, 255, 0.3)',
          transition: 'all 0.3s ease',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.1) translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 12px 32px rgba(0, 123, 255, 0.4)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1) translateY(0)';
          e.currentTarget.style.boxShadow = '0 8px 24px rgba(0, 123, 255, 0.3)';
        }}
        title="Talk to Exie AI"
      >
        🤖
      </button>
    </div>
  );
};

MeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default MeetingPage;
