'use client';

import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const MeetingPage: NextPageWithLayout = () => {
  // Use a reliable public Daily.co room that works without API key
  // This is Daily.co's official demo room that's always available
  const roomUrl = 'https://demo.daily.co/hello';
  const loading = false;

  // Render loading state
  if (loading) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f8f8'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: 'var(--accent-color)', marginBottom: '16px' }}>Setting up your meeting...</h2>
          <div style={{ color: '#666' }}>Please wait while we prepare your room</div>
        </div>
      </div>
    );
  }





  // Render the UI only if roomUrl is available
  if (!roomUrl) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f8f8'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: 'var(--accent-color)', marginBottom: '16px' }}>Setting up your meeting...</h2>
          <div style={{ color: '#666' }}>Please wait while we prepare your room</div>
        </div>
      </div>
    );
  }

  // Show loading state while creating room
  if (loading) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f8f8'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: 'var(--accent-color)', marginBottom: '16px' }}>Setting up your meeting...</h2>
          <div style={{ color: '#666' }}>Please wait while we prepare your room</div>
        </div>
      </div>
    );
  }

  // Show error if no room URL
  if (!roomUrl) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f8f8'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2 style={{ color: '#dc3545', marginBottom: '16px' }}>Unable to create meeting room</h2>
          <div style={{ color: '#666', marginBottom: '16px' }}>Please try refreshing the page</div>
          <button
            onClick={() => window.location.reload()}
            style={{
              backgroundColor: 'var(--accent-color)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '12px 24px',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', backgroundColor: '#f8f8f8', position: 'relative' }}>
      {/* Header with meeting info */}
      <div style={{
        padding: '16px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ color: 'var(--accent-color)', margin: 0, fontSize: '20px' }}>Exie AI Meeting</h1>
        <div style={{ fontSize: '14px', color: '#666' }}>
          AI-powered mentoring session with Daily.co
        </div>
      </div>

      {/* Daily.co iframe - this gives us the full Google Meet-style UI */}
      <iframe
        src={roomUrl}
        allow="camera; microphone; fullscreen; display-capture; autoplay"
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          borderRadius: '0 0 8px 8px'
        }}
        title="Daily.co Video Meeting"
      />

      {/* Floating AI Assistant Button */}
      <button
        onClick={() => alert('AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.')}
        style={{
          position: 'absolute',
          bottom: '24px',
          right: '24px',
          backgroundColor: 'var(--accent-color)',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '60px',
          height: '60px',
          fontSize: '24px',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
          transition: 'all 0.2s ease',
          zIndex: 1000
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.1)';
          e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';
        }}
        title="Talk to Exie AI"
      >
        🤖
      </button>
    </div>
  );
};

MeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default MeetingPage;