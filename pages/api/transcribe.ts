import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const apiKey = process.env.DEEPGRAM_API_KEY;
  if (!apiKey) {
    return res.status(500).json({ error: 'Missing Deepgram API key' });
  }

  try {
    // For now, return a mock transcript since we don't have Deepgram API key
    const mockTranscript = "Hello, this is a mock transcript for testing purposes.";
    return res.status(200).json({ transcript: mockTranscript });
    
    // Uncomment below when you have Deepgram API key
    /*
    const audioBuffer = req.body;
    
    const dgRes = await fetch('https://api.deepgram.com/v1/listen', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${apiKey}`,
        'Content-Type': 'audio/webm',
      },
      body: audioBuffer,
    });
    
    if (!dgRes.ok) throw new Error('Deepgram error');
    const dgData = await dgRes.json();
    const transcript = dgData.results?.channels?.[0]?.alternatives?.[0]?.transcript || '';
    return res.status(200).json({ transcript });
    */
  } catch (err) {
    return res.status(500).json({ error: 'Error contacting Deepgram.' });
  }
}
