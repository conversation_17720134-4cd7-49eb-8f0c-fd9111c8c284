import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { roomName } = req.body;
  const apiKey = process.env.DAILY_API_KEY;

  if (!roomName) {
    return res.status(400).json({ error: 'Room name is required' });
  }

  try {
    if (!apiKey) {
      // Return a working public Daily.co room for testing
      console.log('No Daily.co API key found, returning public demo room URL');
      // Use Daily.co's public demo room that actually works
      const demoRoomUrl = 'https://demo.daily.co/hello';
      return res.status(200).json({ url: demoRoomUrl });
    }

    // Create a room using Daily.co REST API
    const response = await fetch('https://api.daily.co/v1/rooms', {
      method: 'POST',
      headers: {
        'Authorization': `Bear<PERSON> ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: roomName,
        properties: {
          max_participants: 10,
          enable_chat: true,
          enable_screenshare: true,
          enable_recording: false,
          start_video_off: false,
          start_audio_off: false,
          exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24), // Expire in 24 hours
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Daily.co API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
    }

    const data = await response.json();
    return res.status(200).json({ url: data.url });
  } catch (error) {
    console.error('Error creating Daily.co room:', error);
    return res.status(500).json({ error: 'Error creating room.' });
  }
}
