import { NextApiRequest, NextApiResponse } from 'next';
import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { message, tweetsOrHandle } = req.body;

  // Compose prompt with context
  const prompt = tweetsOrHandle
    ? `User's context: ${tweetsOrHandle}\n\nQuestion: ${message}`
    : message;

  try {
    if (!process.env.OPENAI_API_KEY) {
      // Return mock response for testing
      const mockReply = "Hello! I'm <PERSON><PERSON>, your AI mentor. I'm here to help you grow your presence and achieve your goals. What would you like to discuss today?";
      return res.status(200).json({ reply: mockReply });
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: 'You are <PERSON><PERSON>, an expert AI mentor and growth coach. Give actionable, friendly, and specific advice to help users grow their online presence and achieve their goals.' },
        { role: 'user', content: prompt }
      ],
      max_tokens: 400
    });
    
    const reply = completion.choices[0]?.message?.content || 'Sorry, I could not generate a response.';
    return res.status(200).json({ reply });
  } catch (err) {
    console.error('OpenAI API error:', err);
    return res.status(500).json({ reply: 'Error contacting OpenAI.' });
  }
}
