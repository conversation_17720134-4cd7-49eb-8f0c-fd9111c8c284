// pages/index.tsx
import Link from 'next/link';
import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const HomePage: NextPageWithLayout = () => {
  const colors = {
    primary: '#F97316',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    border: '#E5E7EB',
    surface: '#FFFFFF',
    background: '#F8F9FA'
  };

  return (
    <div style={{ padding: '32px' }}>
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          color: colors.text.primary,
          marginBottom: '8px',
          fontSize: '32px',
          fontWeight: '700',
          letterSpacing: '-0.5px'
        }}>
          Welcome to Exie AI
        </h1>
        <p style={{
          color: colors.text.secondary,
          fontSize: '18px',
          margin: 0,
          marginBottom: '24px'
        }}>
          Your intelligent AI assistant for meetings, social media, and analytics
        </p>

        <div style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '8px',
          padding: '12px 20px',
          backgroundColor: colors.primary,
          color: 'white',
          borderRadius: '8px',
          textDecoration: 'none',
          fontWeight: '600',
          fontSize: '14px',
          transition: 'all 0.2s ease',
          cursor: 'pointer'
        }}>
          <Link href="/meeting" style={{ color: 'inherit', textDecoration: 'none' }}>
            Start Meeting →
          </Link>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginTop: '32px'
      }}>
        <div style={{
          backgroundColor: colors.surface,
          padding: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          border: `1px solid ${colors.border}`,
        }}>
          <h3 style={{
            color: colors.text.primary,
            marginBottom: '12px',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            AI Meetings
          </h3>
          <p style={{
            color: colors.text.secondary,
            fontSize: '14px',
            margin: 0
          }}>
            Host intelligent video calls with AI assistance
          </p>
        </div>

        <div style={{
          backgroundColor: colors.surface,
          padding: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          border: `1px solid ${colors.border}`,
        }}>
          <h3 style={{
            color: colors.text.primary,
            marginBottom: '12px',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            Social Hub
          </h3>
          <p style={{
            color: colors.text.secondary,
            fontSize: '14px',
            margin: 0
          }}>
            Manage your social media presence with AI
          </p>
        </div>

        <div style={{
          backgroundColor: colors.surface,
          padding: '20px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          border: `1px solid ${colors.border}`,
        }}>
          <h3 style={{
            color: colors.text.primary,
            marginBottom: '12px',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            Analytics
          </h3>
          <p style={{
            color: colors.text.secondary,
            fontSize: '14px',
            margin: 0
          }}>
            Track performance and gain insights
          </p>
        </div>
      </div>
    </div>
  );
};

HomePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default HomePage;