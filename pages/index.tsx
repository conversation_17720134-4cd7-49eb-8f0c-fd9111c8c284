// pages/index.tsx
import Link from 'next/link';
import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const HomePage: NextPageWithLayout = () => {
  const colors = {
    primary: '#F97316',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    border: '#E5E7EB',
    surface: '#FFFFFF',
    background: '#F8F9FA'
  };

  return (
    <div style={{ padding: '40px', minHeight: '100vh' }}>
      {/* Briefing Room Header */}
      <div style={{ marginBottom: '40px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '16px' }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '16px',
            background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: `0 8px 24px ${colors.primary}30`
          }}>
            <span style={{ fontSize: '24px' }}>🏠</span>
          </div>
          <div>
            <h1 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '32px',
              fontWeight: '700',
              letterSpacing: '-1px'
            }}>
              Briefing Room
            </h1>
            <p style={{
              color: colors.text.secondary,
              fontSize: '16px',
              margin: 0
            }}>
              Your daily mission control center
            </p>
          </div>
        </div>
      </div>

      {/* Today's Mission Card */}
      <div style={{
        background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B220 100%)`,
        borderRadius: '24px',
        padding: '32px',
        marginBottom: '32px',
        boxShadow: `
          0 20px 60px rgba(0, 0, 0, 0.08),
          0 8px 32px rgba(0, 0, 0, 0.04),
          inset 0 1px 0 rgba(255, 255, 255, 0.9)
        `,
        border: `1px solid ${colors.border}`,
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Ambient glow */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '100px',
          background: `radial-gradient(ellipse at top, #FFE0B230 0%, transparent 70%)`,
          pointerEvents: 'none'
        }} />

        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '10px',
              background: `linear-gradient(135deg, ${colors.primary}20 0%, #FF8A6520 100%)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ fontSize: '16px' }}>🎯</span>
            </div>
            <h2 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '24px',
              fontWeight: '600'
            }}>
              Today's Mission
            </h2>
            <div style={{
              padding: '4px 12px',
              background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,
              borderRadius: '12px',
              color: 'white',
              fontSize: '12px',
              fontWeight: '600'
            }}>
              AI Generated
            </div>
          </div>

          <p style={{
            color: colors.text.secondary,
            fontSize: '18px',
            lineHeight: '1.6',
            margin: 0,
            marginBottom: '24px'
          }}>
            "Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow."
          </p>

          {/* Performance Summary */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '20px',
            marginBottom: '24px'
          }}>
            {[
              { label: 'Engagement Rate', value: '+24%', icon: '📈' },
              { label: 'New Followers', value: '127', icon: '👥' },
              { label: 'Content Score', value: '8.9/10', icon: '⭐' }
            ].map((stat, index) => (
              <div key={index} style={{
                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)`,
                borderRadius: '16px',
                padding: '20px',
                textAlign: 'center',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.5)'
              }}>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>{stat.icon}</div>
                <div style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: colors.text.primary,
                  marginBottom: '4px'
                }}>
                  {stat.value}
                </div>
                <div style={{
                  fontSize: '14px',
                  color: colors.text.tertiary,
                  fontWeight: '500'
                }}>
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div style={{
            display: 'flex',
            gap: '16px',
            flexWrap: 'wrap'
          }}>
            <Link href="/meeting" style={{ textDecoration: 'none' }}>
              <button style={{
                padding: '16px 24px',
                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: `0 8px 24px ${colors.primary}40`,
                transition: 'all 0.3s ease'
              }}>
                🎥 Join Call
              </button>
            </Link>

            <button style={{
              padding: '16px 24px',
              background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,
              color: colors.text.primary,
              border: `1px solid ${colors.border}`,
              borderRadius: '16px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: 'pointer',
              backdropFilter: 'blur(10px)',
              transition: 'all 0.3s ease'
            }}>
              🤖 Ask Mentor
            </button>

            <Link href="/tweet-center" style={{ textDecoration: 'none' }}>
              <button style={{
                padding: '16px 24px',
                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,
                color: colors.text.primary,
                border: `1px solid ${colors.border}`,
                borderRadius: '16px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'pointer',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease'
              }}>
                ✍️ Generate Tweet
              </button>
            </Link>
          </div>
        </div>
      </div>

      {/* Mentor Avatar Section */}
      <div style={{
        background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B215 100%)`,
        borderRadius: '20px',
        padding: '24px',
        boxShadow: `
          0 12px 40px rgba(0, 0, 0, 0.06),
          inset 0 1px 0 rgba(255, 255, 255, 0.8)
        `,
        border: `1px solid ${colors.border}`
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{
            width: '64px',
            height: '64px',
            borderRadius: '20px',
            background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: `0 8px 24px ${colors.primary}30`,
            position: 'relative'
          }}>
            <span style={{ fontSize: '32px' }}>🧠</span>
            <div style={{
              position: 'absolute',
              bottom: '-2px',
              right: '-2px',
              width: '16px',
              height: '16px',
              borderRadius: '50%',
              background: '#00E676',
              border: '2px solid white',
              boxShadow: '0 0 8px rgba(0, 230, 118, 0.6)'
            }} />
          </div>
          <div style={{ flex: 1 }}>
            <h3 style={{
              color: colors.text.primary,
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '4px'
            }}>
              AI Mentor
            </h3>
            <p style={{
              color: colors.text.secondary,
              margin: 0,
              fontSize: '16px',
              fontStyle: 'italic'
            }}>
              "Ready to help you create content that resonates. What's on your mind today?"
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

HomePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default HomePage;