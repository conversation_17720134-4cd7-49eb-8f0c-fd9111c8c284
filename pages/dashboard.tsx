// pages/dashboard.tsx
import React from 'react';
import SidebarLayout from '../components/SidebarLayout';
import type { ReactElement } from 'react';
import type { NextPageWithLayout } from './_app';

const DashboardPage: NextPageWithLayout = () => {
  const colors = {
    primary: '#F97316',
    text: {
      primary: '#1F2937',
      secondary: '#6B7280',
      tertiary: '#9CA3AF'
    },
    border: '#E5E7EB',
    surface: '#FFFFFF',
    background: '#F8F9FA'
  };

  return (
    <div style={{ padding: '32px', height: '100vh', overflow: 'auto' }}>
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          color: colors.text.primary,
          margin: 0,
          fontSize: '28px',
          fontWeight: '600',
          letterSpacing: '-0.5px',
          marginBottom: '8px'
        }}>
          Growth Lab
        </h1>
        <p style={{
          color: colors.text.secondary,
          fontSize: '16px',
          margin: 0,
          fontWeight: '400'
        }}>
          Monitor your AI assistant performance and insights
        </p>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
        gap: '24px',
      }}>
        <div style={{
          backgroundColor: colors.surface,
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          border: `1px solid ${colors.border}`,
        }}>
          <h2 style={{
            color: colors.text.primary,
            marginBottom: '20px',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            Quick Stats
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ color: colors.text.secondary }}>Total Meetings:</span>
              <strong style={{ color: colors.primary, fontSize: '18px' }}>150</strong>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ color: colors.text.secondary }}>Social Posts:</span>
              <strong style={{ color: colors.primary, fontSize: '18px' }}>500</strong>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span style={{ color: colors.text.secondary }}>Avg Duration:</span>
              <strong style={{ color: colors.primary, fontSize: '18px' }}>30 mins</strong>
            </div>
          </div>
        </div>

        <div style={{
          backgroundColor: colors.surface,
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          border: `1px solid ${colors.border}`,
        }}>
          <h2 style={{
            color: colors.text.primary,
            marginBottom: '20px',
            fontSize: '18px',
            fontWeight: '600'
          }}>
            Recent Activity
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{
              paddingBottom: '12px',
              borderBottom: `1px solid ${colors.border}`,
              fontSize: '14px',
              color: colors.text.secondary
            }}>
              Meeting with John Doe completed
            </div>
            <div style={{
              paddingBottom: '12px',
              borderBottom: `1px solid ${colors.border}`,
              fontSize: '14px',
              color: colors.text.secondary
            }}>
              Social post about new feature published
            </div>
            <div style={{
              paddingBottom: '12px',
              borderBottom: `1px solid ${colors.border}`,
              fontSize: '14px',
              color: colors.text.secondary
            }}>
              Meeting with Jane Smith scheduled
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

DashboardPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <SidebarLayout>
      {page}
    </SidebarLayout>
  );
};

export default DashboardPage;