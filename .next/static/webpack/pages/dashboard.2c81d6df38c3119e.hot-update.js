"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Premium color palette - Light Sky Blue theme\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\" // Primary colored border\n        },\n        shadow: {\n            subtle: \"rgba(15, 23, 42, 0.04)\",\n            medium: \"rgba(15, 23, 42, 0.08)\",\n            strong: \"rgba(15, 23, 42, 0.12)\",\n            primary: \"rgba(14, 165, 233, 0.15)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: \"⌂\",\n            description: \"Dashboard & Analytics\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Intelligence\",\n            icon: \"◈\",\n            description: \"AI Insights & Reports\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social Hub\",\n            icon: \"◉\",\n            description: \"Content & Engagement\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Conferences\",\n            icon: \"◎\",\n            description: \"Video & Collaboration\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.surfaceElevated\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"320px\",\n                    background: colors.surface,\n                    borderRight: \"1px solid \".concat(colors.border.light),\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    boxShadow: \"\\n          0 0 0 1px \".concat(colors.border.light, \",\\n          0 4px 24px \").concat(colors.shadow.subtle, \",\\n          0 8px 48px \").concat(colors.shadow.medium, \"\\n        \"),\n                    zIndex: 100\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"40px 32px 32px 32px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light),\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.accent, \" 100%)\"),\n                            position: \"relative\",\n                            overflow: \"hidden\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    background: \"\\n              radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"08 0%, transparent 50%),\\n              radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"06 0%, transparent 50%)\\n            \"),\n                                    pointerEvents: \"none\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"48px\",\n                                            height: \"48px\",\n                                            borderRadius: \"12px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginBottom: \"16px\",\n                                            boxShadow: \"\\n                0 4px 12px \".concat(colors.shadow.primary, \",\\n                0 0 0 1px \").concat(colors.border.primary, \"20\\n              \")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"24px\",\n                                                color: colors.text.inverse,\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"E\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"28px\",\n                                            fontWeight: \"700\",\n                                            letterSpacing: \"-0.8px\",\n                                            color: colors.text.primary,\n                                            lineHeight: \"1.2\"\n                                        },\n                                        children: \"Exie\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            margin: \"4px 0 0 0\",\n                                            fontSize: \"15px\",\n                                            color: colors.text.secondary,\n                                            fontWeight: \"500\",\n                                            letterSpacing: \"0.2px\"\n                                        },\n                                        children: \"Enterprise AI Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"24px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            style: {\n                                listStyle: \"none\",\n                                padding: 0,\n                                margin: 0\n                            },\n                            children: menuItems.map((item)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"16px 20px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                backgroundColor: active ? \"rgba(102, 126, 234, 0.1)\" : hovered ? \"rgba(0, 0, 0, 0.04)\" : \"transparent\",\n                                                border: active ? \"1px solid rgba(102, 126, 234, 0.2)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"inset 0 1px 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0,0,0,0.05)\" : hovered ? \"inset 0 1px 2px rgba(0,0,0,0.05), 0 1px 3px rgba(0,0,0,0.05)\" : \"none\",\n                                                transform: hovered ? \"translateX(4px)\" : \"translateX(0)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        backgroundColor: \"#667eea\",\n                                                        borderRadius: \"0 2px 2px 0\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"20px\",\n                                                        marginRight: \"16px\",\n                                                        transition: \"transform 0.2s ease\",\n                                                        transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"16px\",\n                                                                fontWeight: active ? \"600\" : \"500\",\n                                                                color: active ? \"#667eea\" : \"#2c3e50\",\n                                                                marginBottom: \"2px\",\n                                                                transition: \"color 0.2s ease\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: \"#6c757d\",\n                                                                opacity: hovered || active ? 1 : 0.7,\n                                                                transition: \"opacity 0.2s ease\"\n                                                            },\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#667eea\",\n                                                        opacity: 0.7,\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: \"24px\",\n                            left: \"16px\",\n                            right: \"16px\",\n                            padding: \"16px\",\n                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                            borderRadius: \"12px\",\n                            border: \"1px solid rgba(102, 126, 234, 0.1)\",\n                            boxShadow: \"inset 0 1px 2px rgba(102, 126, 234, 0.05)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    color: \"#667eea\",\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"\\uD83D\\uDCA1 Pro Tip\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6c757d\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: \"Use AI Meeting for personalized mentoring sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8f9fa\",\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});