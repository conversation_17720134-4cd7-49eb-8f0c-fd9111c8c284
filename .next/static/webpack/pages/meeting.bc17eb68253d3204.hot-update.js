"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @daily-co/daily-react */ \"./node_modules/@daily-co/daily-react/dist/daily-react.esm.js\");\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MeetingPage = ()=>{\n    _s();\n    // Bring back the state for controls and Exie interaction\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [micOn, setMicOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [cameraOn, setCameraOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [exieLoading, setExieLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exieVideoUrl, setExieVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Exie's video URL\n    const [exieTranscript, setExieTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Exie's transcript\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setJoined(false); // Start as not joined, will join when user clicks join\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setJoined(false);\n            }\n        };\n        createRoom();\n    }, []); // Run once on component mount\n    // Handlers for controls - Implement the logic here\n    const handleToggleMic = async ()=>{\n        // Toggle mic logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setMicOn(!micOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the micOn prop.\n    };\n    const handleToggleCamera = async ()=>{\n        // Toggle camera logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setCameraOn(!cameraOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the cameraOn prop.\n    };\n    const handleJoin = async ()=>{\n        setJoined(true);\n    };\n    const handleLeave = async ()=>{\n        setJoined(false);\n    // Optionally reload to reset state\n    // window.location.reload();\n    };\n    // Handle Talk to Exie logic (re-implemented here)\n    const handleTalkToExie = async ()=>{\n        if (!micOn || exieLoading) return; // Ensure mic state is on and not already loading\n        setExieLoading(true);\n        setExieTranscript(\"\"); // Clear previous transcript\n        setExieVideoUrl(null); // Clear previous video\n        console.log(\"Starting audio recording...\");\n        let mediaRecorder = null;\n        let stream = null;\n        try {\n            // Get a new audio stream specifically for recording\n            stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm\"\n            });\n            const audioChunks = [];\n            mediaRecorder.ondataavailable = (e)=>{\n                if (e.data.size > 0) {\n                    audioChunks.push(e.data);\n                    console.log(\"Collected audio chunk:\", e.data.size, \"bytes\");\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                console.log(\"Audio recording stopped. Total chunks:\", audioChunks.length);\n                const audioBlob = new Blob(audioChunks, {\n                    type: \"audio/webm\"\n                });\n                console.log(\"Audio Blob created:\", audioBlob.size, \"bytes\", audioBlob.type);\n                // Send to /api/transcribe\n                console.log(\"Sending audio to /api/transcribe...\");\n                const res = await fetch(\"/api/transcribe\", {\n                    method: \"POST\",\n                    body: audioBlob,\n                    headers: {\n                        \"Content-Type\": \"audio/webm\"\n                    }\n                });\n                if (!res.ok) throw new Error(\"Transcribe API error: \".concat(res.status));\n                const data = await res.json();\n                const transcript = data.transcript || \"\";\n                console.log(\"Transcript received:\", transcript);\n                setExieTranscript(transcript); // Update Exie\\'s transcript state\n                if (transcript) {\n                    var _voiceData_audioBase64;\n                    // Send to AI pipeline (gpt -> voice -> video)\n                    console.log(\"Sending transcript to /api/gpt...\");\n                    const gptRes = await fetch(\"/api/gpt\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            message: transcript,\n                            tweetsOrHandle: \"\"\n                        })\n                    });\n                    if (!gptRes.ok) throw new Error(\"GPT API error: \".concat(gptRes.status));\n                    const gptData = await gptRes.json();\n                    const mentorReply = gptData.reply || \"Sorry, I could not generate a response.\";\n                    console.log(\"GPT Reply received:\", mentorReply);\n                    setExieTranscript(mentorReply); // Update Exie\\'s transcript state with reply\n                    // ElevenLabs\n                    console.log(\"Sending text to /api/voice...\");\n                    const voiceRes = await fetch(\"/api/voice\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            text: mentorReply\n                        })\n                    });\n                    if (!voiceRes.ok) throw new Error(\"Voice API error: \".concat(voiceRes.status));\n                    const voiceData = await voiceRes.json();\n                    console.log(\"Voice data received (base64 length):\", (_voiceData_audioBase64 = voiceData.audioBase64) === null || _voiceData_audioBase64 === void 0 ? void 0 : _voiceData_audioBase64.length);\n                    if (!voiceData.audioBase64) throw new Error(\"No audio returned from ElevenLabs.\");\n                    // Create a blob URL for the audio\n                    const audioBlob = new Blob([\n                        Uint8Array.from(atob(voiceData.audioBase64), (c)=>c.charCodeAt(0))\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioBlob);\n                    console.log(\"Audio Blob URL created:\", audioUrl);\n                    // D-ID\n                    console.log(\"Sending audio URL to /api/video...\");\n                    const videoRes = await fetch(\"/api/video\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            audioUrl,\n                            imageUrl: \"https://via.placeholder.com/320x240.png?text=Exie+AI\"\n                        }) // Use consistent placeholder\n                    });\n                    if (!videoRes.ok) throw new Error(\"Video API error: \".concat(videoRes.status));\n                    const videoData = await videoRes.json();\n                    console.log(\"Video URL received:\", videoData.videoUrl);\n                    if (videoData.videoUrl) setExieVideoUrl(videoData.videoUrl); // Update Exie\\'s video URL state\n                    // Play the audio\n                    const audio = new Audio(audioUrl);\n                    audio.play();\n                } else {\n                    console.log(\"No transcript to process.\");\n                }\n            };\n            mediaRecorder.start();\n            // Stop recording after a short delay (adjust as needed)\n            setTimeout(()=>{\n                if ((mediaRecorder === null || mediaRecorder === void 0 ? void 0 : mediaRecorder.state) !== \"inactive\") mediaRecorder === null || mediaRecorder === void 0 ? void 0 : mediaRecorder.stop();\n                // Stop the getUserMedia tracks after recording stops\n                stream === null || stream === void 0 ? void 0 : stream.getTracks().forEach((track)=>track.stop());\n            }, 5000); // Record for 5 seconds max\n        } catch (error) {\n            console.error(\"Error recording audio or processing AI pipeline:\", error);\n            setExieTranscript(\"Sorry, there was an error processing your request.\"); // Update Exie\\'s transcript state on error\n        } finally{\n            setExieLoading(false);\n        }\n    };\n    // Effect to join the Agora channel when component mounts and agoraClient is ready (now handled in VideoCallGrid)\n    // However, we need to set \\'joined\\' state based on connection status.\n    // We\\'ll rely on VideoCallGrid to emit a \\'joined\\' status change or similar if needed, or manage joining entirely in VideoCallGrid\\'s hook.\n    // For simplicity now, assume VideoCallGrid\\'s useJoin hook is sufficient to manage the connection, and we\\'ll rely on the mic/camera tracks being ready to indicate a potential \\'joined\\' state for controls visibility.\n    // A more robust approach might involve a context or callback from VideoCallGrid when truly connected.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simple way to set joined state based on whether local tracks are available\n        if (micOn && cameraOn) {\n        // This is a simplification. A real \\'joined\\' status should come from Agora\\'s connection state.\n        // We will pass the \\'joined\\' prop down, and VideoCallGrid\\'s useJoin will attempt to join.\n        // We might need a mechanism to get the actual connection state back up from VideoCallGrid.\n        // For now, let\\'s rely on VideoCallGrid\\'s internal joined state derived from the Agora client listener\n        // and pass down the mic/camera state and handlers.\n        // The \\'joined\\' state in MeetingPage will primarily control visibility of the footer controls.\n        // Re-add connection state listener if not fully handled by VideoCallGrid\n        // If VideoCallGrid\\'s useJoin manages connection and updates an internal state,\n        // we might need a way for VideoCallGrid to communicate the joined status up.\n        // Let\\'s stick to the current plan: VideoCallGrid handles Agora, MeetingPage handles overall UI and AI pipeline.\n        // MeetingPage\\'s \\'joined\\' state will primarily control visibility of the footer controls.\n        // A potential approach: VideoCallGrid calls a prop function like onJoinedStatusChange(status: boolean)\n        // useEffect(() => {\n        //   if (agoraClient) {\n        //     agoraClient.on(\\'connection-state-change\\', (state) => {\n        //       if (state === \\'CONNECTED\\') {\n        //         setJoined(true);\n        //       } else {\n        //         setJoined(false);\n        //       }\n        //     });\n        //   }\n        // }, [agoraClient]);\n        // For Daily.co, the useRoom hook\\'s state might be a better indicator of joined status.\n        // Let\\'s update the joined state based on the presence of a room URL for now, as done in the initial effect.\n        }\n    }, [\n        micOn,\n        cameraOn\n    ]); // Depend on micOn and cameraOn to re-check joined status (simplified)\n    // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined);\n    }\n    return(// Wrap the meeting content in DailyProvider to provide the Daily room context to children\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyProvider, {\n        url: roomUrl,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: [\n                \" \",\n                !joined ? // Show join screen when not joined\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: \"100vh\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        flexDirection: \"column\",\n                        gap: \"24px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"var(--accent-color)\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ready to meet with Exie?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: \"#666\",\n                                    marginBottom: \"32px\"\n                                },\n                                children: \"Join the meeting to start your AI mentoring session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoin,\n                                style: {\n                                    backgroundColor: \"var(--accent-color)\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    padding: \"12px 24px\",\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.opacity = \"0.9\",\n                                onMouseOut: (e)=>e.currentTarget.style.opacity = \"1\",\n                                children: \"Join Meeting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoCallGrid, {\n                            joined: joined,\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            exieLoading: exieLoading,\n                            exieVideoUrl: exieVideoUrl,\n                            exieTranscript: exieTranscript,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MeetingControls, {\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie,\n                            exieLoading: exieLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 252,\n            columnNumber: 8\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, undefined));\n};\n_s(MeetingPage, \"3iEaDbr8FwW7oQDt8Dd7ZGy125I=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});