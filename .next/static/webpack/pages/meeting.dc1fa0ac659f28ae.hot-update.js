"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Premium color palette - Light Sky Blue theme\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\" // Primary colored border\n        },\n        shadow: {\n            subtle: \"rgba(15, 23, 42, 0.04)\",\n            medium: \"rgba(15, 23, 42, 0.08)\",\n            strong: \"rgba(15, 23, 42, 0.12)\",\n            primary: \"rgba(14, 165, 233, 0.15)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: \"⌂\",\n            description: \"Dashboard & Analytics\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Intelligence\",\n            icon: \"◈\",\n            description: \"AI Insights & Reports\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social Hub\",\n            icon: \"◉\",\n            description: \"Content & Engagement\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Conferences\",\n            icon: \"◎\",\n            description: \"Video & Collaboration\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"280px\",\n                    background: \"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)\",\n                    borderRight: \"1px solid #e9ecef\",\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    boxShadow: \"inset -1px 0 0 rgba(0,0,0,0.05)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 24px 24px 24px\",\n                            borderBottom: \"1px solid #f1f3f4\",\n                            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n                            color: \"white\",\n                            position: \"relative\",\n                            overflow: \"hidden\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"-50%\",\n                                    right: \"-50%\",\n                                    width: \"200%\",\n                                    height: \"200%\",\n                                    background: \"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)\",\n                                    pointerEvents: \"none\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    margin: 0,\n                                    fontSize: \"24px\",\n                                    fontWeight: \"700\",\n                                    letterSpacing: \"-0.5px\",\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: \"Exie AI\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: \"4px 0 0 0\",\n                                    fontSize: \"14px\",\n                                    opacity: 0.9,\n                                    fontWeight: \"400\",\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: \"Your AI Assistant\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"24px 16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            style: {\n                                listStyle: \"none\",\n                                padding: 0,\n                                margin: 0\n                            },\n                            children: menuItems.map((item)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"16px 20px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                backgroundColor: active ? \"rgba(102, 126, 234, 0.1)\" : hovered ? \"rgba(0, 0, 0, 0.04)\" : \"transparent\",\n                                                border: active ? \"1px solid rgba(102, 126, 234, 0.2)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"inset 0 1px 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0,0,0,0.05)\" : hovered ? \"inset 0 1px 2px rgba(0,0,0,0.05), 0 1px 3px rgba(0,0,0,0.05)\" : \"none\",\n                                                transform: hovered ? \"translateX(4px)\" : \"translateX(0)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        backgroundColor: \"#667eea\",\n                                                        borderRadius: \"0 2px 2px 0\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"20px\",\n                                                        marginRight: \"16px\",\n                                                        transition: \"transform 0.2s ease\",\n                                                        transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"16px\",\n                                                                fontWeight: active ? \"600\" : \"500\",\n                                                                color: active ? \"#667eea\" : \"#2c3e50\",\n                                                                marginBottom: \"2px\",\n                                                                transition: \"color 0.2s ease\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: \"#6c757d\",\n                                                                opacity: hovered || active ? 1 : 0.7,\n                                                                transition: \"opacity 0.2s ease\"\n                                                            },\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        color: \"#667eea\",\n                                                        opacity: 0.7,\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: \"24px\",\n                            left: \"16px\",\n                            right: \"16px\",\n                            padding: \"16px\",\n                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                            borderRadius: \"12px\",\n                            border: \"1px solid rgba(102, 126, 234, 0.1)\",\n                            boxShadow: \"inset 0 1px 2px rgba(102, 126, 234, 0.05)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    color: \"#667eea\",\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"\\uD83D\\uDCA1 Pro Tip\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6c757d\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: \"Use AI Meeting for personalized mentoring sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8f9fa\",\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});