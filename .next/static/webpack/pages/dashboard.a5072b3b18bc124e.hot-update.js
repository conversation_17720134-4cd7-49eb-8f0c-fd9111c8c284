"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Premium color palette - Light Sky Blue theme\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\" // Primary colored border\n        },\n        shadow: {\n            subtle: \"rgba(15, 23, 42, 0.04)\",\n            medium: \"rgba(15, 23, 42, 0.08)\",\n            strong: \"rgba(15, 23, 42, 0.12)\",\n            primary: \"rgba(14, 165, 233, 0.15)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: \"⌂\",\n            description: \"Dashboard & Analytics\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Intelligence\",\n            icon: \"◈\",\n            description: \"AI Insights & Reports\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social Hub\",\n            icon: \"◉\",\n            description: \"Content & Engagement\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Conferences\",\n            icon: \"◎\",\n            description: \"Video & Collaboration\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.surfaceElevated\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"320px\",\n                    background: colors.surface,\n                    borderRight: \"1px solid \".concat(colors.border.light),\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    boxShadow: \"\\n          0 0 0 1px \".concat(colors.border.light, \",\\n          0 4px 24px \").concat(colors.shadow.subtle, \",\\n          0 8px 48px \").concat(colors.shadow.medium, \"\\n        \"),\n                    zIndex: 100\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"40px 32px 32px 32px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light),\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.accent, \" 100%)\"),\n                            position: \"relative\",\n                            overflow: \"hidden\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    background: \"\\n              radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"08 0%, transparent 50%),\\n              radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"06 0%, transparent 50%)\\n            \"),\n                                    pointerEvents: \"none\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"48px\",\n                                            height: \"48px\",\n                                            borderRadius: \"12px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginBottom: \"16px\",\n                                            boxShadow: \"\\n                0 4px 12px \".concat(colors.shadow.primary, \",\\n                0 0 0 1px \").concat(colors.border.primary, \"20\\n              \")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"24px\",\n                                                color: colors.text.inverse,\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"E\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"28px\",\n                                            fontWeight: \"700\",\n                                            letterSpacing: \"-0.8px\",\n                                            color: colors.text.primary,\n                                            lineHeight: \"1.2\"\n                                        },\n                                        children: \"Exie\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            margin: \"4px 0 0 0\",\n                                            fontSize: \"15px\",\n                                            color: colors.text.secondary,\n                                            fontWeight: \"500\",\n                                            letterSpacing: \"0.2px\"\n                                        },\n                                        children: \"Enterprise AI Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            padding: \"32px 24px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            style: {\n                                listStyle: \"none\",\n                                padding: 0,\n                                margin: 0\n                            },\n                            children: menuItems.map((item)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    style: {\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"20px 24px\",\n                                                borderRadius: \"16px\",\n                                                transition: \"all 0.4s cubic-bezier(0.16, 1, 0.3, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                backgroundColor: active ? colors.accent : hovered ? colors.surfaceElevated : \"transparent\",\n                                                border: active ? \"1px solid \".concat(colors.border.primary, \"40\") : \"1px solid \".concat(colors.border.light),\n                                                boxShadow: active ? \"\\n                          0 0 0 1px \".concat(colors.border.primary, \"20,\\n                          0 4px 16px \").concat(colors.shadow.primary, \",\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n                        \") : hovered ? \"\\n                            0 0 0 1px \".concat(colors.border.medium, \",\\n                            0 4px 12px \").concat(colors.shadow.subtle, \",\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.6)\\n                          \") : \"\\n                            0 0 0 1px transparent,\\n                            0 1px 3px \".concat(colors.shadow.subtle, \"\\n                          \"),\n                                                transform: hovered ? \"translateY(-2px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"32px\",\n                                                        background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                        borderRadius: \"0 4px 4px 0\",\n                                                        boxShadow: \"0 0 8px \".concat(colors.shadow.primary)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"44px\",\n                                                        height: \"44px\",\n                                                        borderRadius: \"12px\",\n                                                        background: active ? \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\") : hovered ? \"linear-gradient(135deg, \".concat(colors.surfaceElevated, \" 0%, \").concat(colors.accent, \" 100%)\") : colors.surfaceElevated,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"20px\",\n                                                        transition: \"all 0.3s ease\",\n                                                        boxShadow: active ? \"\\n                            0 4px 12px \".concat(colors.shadow.primary, \",\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n                          \") : \"\\n                            0 2px 8px \".concat(colors.shadow.subtle, \",\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.5)\\n                          \"),\n                                                        transform: hovered ? \"scale(1.1) rotate(2deg)\" : \"scale(1) rotate(0deg)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"20px\",\n                                                            color: active ? colors.text.inverse : colors.text.secondary,\n                                                            transition: \"color 0.2s ease\",\n                                                            fontWeight: \"600\"\n                                                        },\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"17px\",\n                                                                fontWeight: active ? \"700\" : \"600\",\n                                                                color: active ? colors.primary : colors.text.primary,\n                                                                marginBottom: \"4px\",\n                                                                transition: \"all 0.2s ease\",\n                                                                letterSpacing: \"-0.2px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"13px\",\n                                                                color: colors.text.tertiary,\n                                                                opacity: hovered || active ? 1 : 0.8,\n                                                                transition: \"opacity 0.2s ease\",\n                                                                fontWeight: \"500\",\n                                                                letterSpacing: \"0.1px\"\n                                                            },\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        borderRadius: \"8px\",\n                                                        background: hovered ? \"linear-gradient(135deg, \".concat(colors.primary, \"20 0%, \").concat(colors.primaryLight, \"20 100%)\") : \"transparent\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        transition: \"all 0.2s ease\",\n                                                        opacity: hovered ? 1 : 0,\n                                                        transform: hovered ? \"translateX(0)\" : \"translateX(-8px)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"12px\",\n                                                            color: colors.primary,\n                                                            fontWeight: \"600\"\n                                                        },\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: \"24px\",\n                            left: \"16px\",\n                            right: \"16px\",\n                            padding: \"16px\",\n                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                            borderRadius: \"12px\",\n                            border: \"1px solid rgba(102, 126, 234, 0.1)\",\n                            boxShadow: \"inset 0 1px 2px rgba(102, 126, 234, 0.05)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    color: \"#667eea\",\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"\\uD83D\\uDCA1 Pro Tip\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6c757d\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: \"Use AI Meeting for personalized mentoring sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8f9fa\",\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});