"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./pages/tweet-center.tsx":
/*!********************************!*\
  !*** ./pages/tweet-center.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/tweet-center.tsx\n\nvar _s = $RefreshSig$();\n\n\nconst TweetCenterPage = ()=>{\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiSuggestion, setAiSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"manual\");\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\"\n        },\n        border: \"#F5E6D3\",\n        surface: \"#FFFFFF\",\n        warmGlow: \"#FFE0B2\"\n    };\n    // AI prediction simulation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (content.length > 10 && mode === \"ai\") {\n            const timer = setTimeout(()=>{\n                // Simulate AI prediction based on content\n                if (content.includes(\"AI\")) {\n                    setAiSuggestion(\" can revolutionize your workflow and boost productivity by 10x\");\n                } else if (content.includes(\"productivity\")) {\n                    setAiSuggestion(\" tips that actually work: 1) Time blocking 2) AI automation 3) Single-tasking\");\n                } else if (content.includes(\"building\")) {\n                    setAiSuggestion(\" in public is the fastest way to grow your audience and get feedback\");\n                } else {\n                    setAiSuggestion(\" - here's what I learned from my experience\");\n                }\n                setShowSuggestion(true);\n            }, 500);\n            return ()=>clearTimeout(timer);\n        } else {\n            setShowSuggestion(false);\n        }\n    }, [\n        content,\n        mode\n    ]);\n    const handleTabPress = (e)=>{\n        if (e.key === \"Tab\" && showSuggestion) {\n            e.preventDefault();\n            setContent(content + aiSuggestion);\n            setShowSuggestion(false);\n            setAiSuggestion(\"\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Drafting Desk\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\",\n                            marginBottom: \"20px\"\n                        },\n                        children: \"Your intelligent writing companion\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"4px\",\n                            padding: \"4px\",\n                            background: colors.surface,\n                            borderRadius: \"8px\",\n                            border: \"1px solid \".concat(colors.border),\n                            width: \"fit-content\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"manual\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"manual\" ? colors.primary : \"transparent\",\n                                    color: mode === \"manual\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"Manual\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMode(\"ai\"),\n                                style: {\n                                    padding: \"8px 16px\",\n                                    borderRadius: \"6px\",\n                                    border: \"none\",\n                                    background: mode === \"ai\" ? colors.primary : \"transparent\",\n                                    color: mode === \"ai\" ? \"white\" : colors.text.secondary,\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                children: \"AI Assist\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"300px 1fr 300px\",\n                    gap: \"24px\",\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.warmGlow, \"15 100%)\"),\n                            borderRadius: \"20px\",\n                            padding: \"24px\",\n                            boxShadow: \"\\n            0 12px 40px rgba(0, 0, 0, 0.06),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"18px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: \"\\uD83D\\uDCA1 Ideas & Hooks\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"24px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"12px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Saved Ideas\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        \"AI productivity workflows\",\n                                        \"Building in public lessons\",\n                                        \"Remote work tips\",\n                                        \"Content creation process\"\n                                    ].map((idea, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"12px\",\n                                                background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 100%)\",\n                                                borderRadius: \"12px\",\n                                                marginBottom: \"8px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s ease\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.5)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\"\n                                                },\n                                                children: idea\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"12px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Trending Hooks\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        '\"Here\\'s what I learned...\"',\n                                        '\"The biggest mistake I see...\"',\n                                        '\"3 things that changed my...\"',\n                                        '\"If I started over today...\"'\n                                    ].map((hook, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"12px\",\n                                                background: \"linear-gradient(135deg, \".concat(colors.primary, \"10 0%, \").concat(colors.primaryLight, \"05 100%)\"),\n                                                borderRadius: \"12px\",\n                                                marginBottom: \"8px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s ease\",\n                                                border: \"1px solid \".concat(colors.primary, \"20\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"14px\",\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: hook\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.warmGlow, \"10 100%)\"),\n                            borderRadius: \"20px\",\n                            padding: \"32px\",\n                            boxShadow: \"\\n            0 20px 60px rgba(0, 0, 0, 0.08),\\n            0 8px 32px rgba(0, 0, 0, 0.04),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            position: \"relative\",\n                            overflow: \"hidden\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    height: \"100px\",\n                                    background: \"radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"20 0%, transparent 70%)\"),\n                                    pointerEvents: \"none\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1,\n                                    height: \"100%\",\n                                    display: \"flex\",\n                                    flexDirection: \"column\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"20px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    margin: 0,\n                                                    fontSize: \"20px\",\n                                                    fontWeight: \"600\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: mode === \"ai\" ? \"\\uD83E\\uDD16 AI-Powered Writing\" : \"✏️ Manual Writing\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: colors.text.tertiary,\n                                                    fontSize: \"14px\",\n                                                    margin: 0,\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: \"Start typing and press Tab to accept AI suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            position: \"relative\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: textareaRef,\n                                                value: content,\n                                                onChange: (e)=>setContent(e.target.value),\n                                                onKeyDown: handleTabPress,\n                                                placeholder: mode === \"ai\" ? \"Start writing and I'll help you continue...\" : \"What's on your mind?\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    minHeight: \"300px\",\n                                                    padding: \"24px\",\n                                                    borderRadius: \"16px\",\n                                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)\",\n                                                    backdropFilter: \"blur(10px)\",\n                                                    fontSize: \"18px\",\n                                                    lineHeight: \"1.6\",\n                                                    color: colors.text.primary,\n                                                    fontFamily: \"Georgia, serif\",\n                                                    resize: \"none\",\n                                                    outline: \"none\",\n                                                    boxShadow: \"\\n                    0 8px 32px rgba(0, 0, 0, 0.04),\\n                    inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n                  \",\n                                                    border: \"1px solid \".concat(colors.border)\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            showSuggestion && mode === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"32px\",\n                                                    left: \"32px\",\n                                                    right: \"32px\",\n                                                    padding: \"12px 16px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    borderRadius: \"12px\",\n                                                    color: \"white\",\n                                                    fontSize: \"14px\",\n                                                    boxShadow: \"0 8px 24px \".concat(colors.primary, \"40\"),\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"8px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\uD83D\\uDCA1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            flex: 1,\n                                                            opacity: 0.9\n                                                        },\n                                                        children: aiSuggestion\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            padding: \"4px 8px\",\n                                                            background: \"rgba(255, 255, 255, 0.2)\",\n                                                            borderRadius: \"6px\",\n                                                            fontSize: \"12px\",\n                                                            fontWeight: \"600\"\n                                                        },\n                                                        children: \"Tab\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            marginTop: \"20px\",\n                                            padding: \"16px 0\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    color: colors.text.tertiary,\n                                                    fontSize: \"14px\"\n                                                },\n                                                children: [\n                                                    content.length,\n                                                    \"/280 characters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    gap: \"12px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"12px 20px\",\n                                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)\",\n                                                            color: colors.text.primary,\n                                                            border: \"1px solid \".concat(colors.border),\n                                                            borderRadius: \"12px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            cursor: \"pointer\",\n                                                            backdropFilter: \"blur(10px)\",\n                                                            transition: \"all 0.3s ease\"\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE Save Draft\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            padding: \"12px 20px\",\n                                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                            color: \"white\",\n                                                            border: \"none\",\n                                                            borderRadius: \"12px\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"600\",\n                                                            cursor: \"pointer\",\n                                                            boxShadow: \"0 8px 24px \".concat(colors.primary, \"40\"),\n                                                            transition: \"all 0.3s ease\"\n                                                        },\n                                                        children: \"\\uD83D\\uDE80 Post Tweet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.warmGlow, \"15 100%)\"),\n                            borderRadius: \"20px\",\n                            padding: \"24px\",\n                            boxShadow: \"\\n            0 12px 40px rgba(0, 0, 0, 0.06),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n          \",\n                            border: \"1px solid \".concat(colors.border),\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"18px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: \"\\uD83E\\uDDE0 Mentor Voice\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \"10 0%, \").concat(colors.primaryLight, \"05 100%)\"),\n                                    borderRadius: \"16px\",\n                                    padding: \"20px\",\n                                    marginBottom: \"20px\",\n                                    border: \"1px solid \".concat(colors.primary, \"20\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\",\n                                            marginBottom: \"12px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"32px\",\n                                                    height: \"32px\",\n                                                    borderRadius: \"10px\",\n                                                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"16px\"\n                                                    },\n                                                    children: \"\\uD83C\\uDFAF\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.primary,\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"Real-time Tip\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            margin: 0,\n                                            lineHeight: \"1.5\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: '\"Try starting with a question or bold statement. Your audience loves content that makes them think or challenges their assumptions.\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"12px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"Your Brand Tone\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        {\n                                            tone: \"Helpful\",\n                                            desc: \"Share actionable insights\"\n                                        },\n                                        {\n                                            tone: \"Authentic\",\n                                            desc: \"Be genuine and personal\"\n                                        },\n                                        {\n                                            tone: \"Inspiring\",\n                                            desc: \"Motivate and encourage\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"12px\",\n                                                background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 100%)\",\n                                                borderRadius: \"12px\",\n                                                marginBottom: \"8px\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.5)\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.primary,\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        marginBottom: \"2px\"\n                                                    },\n                                                    children: item.tone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: colors.text.tertiary,\n                                                        fontSize: \"12px\"\n                                                    },\n                                                    children: item.desc\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            marginBottom: \"12px\",\n                                            textTransform: \"uppercase\",\n                                            letterSpacing: \"0.5px\"\n                                        },\n                                        children: \"What's Working\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    [\n                                        'Posts with \"AI\" get 3x engagement',\n                                        \"Questions drive 2x more replies\",\n                                        \"Behind-the-scenes content performs well\"\n                                    ].map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"12px\",\n                                                background: \"linear-gradient(135deg, rgba(0, 230, 118, 0.1) 0%, rgba(0, 200, 83, 0.05) 100%)\",\n                                                borderRadius: \"12px\",\n                                                marginBottom: \"8px\",\n                                                border: \"1px solid rgba(0, 230, 118, 0.2)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.text.secondary,\n                                                    fontSize: \"13px\"\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCC8 \",\n                                                    insight\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TweetCenterPage, \"CedPRLUIuwbmpkl1Fr1+6N+lc+U=\");\n_c = TweetCenterPage;\nTweetCenterPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/tweet-center.tsx\",\n        lineNumber: 535,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (TweetCenterPage);\nvar _c;\n$RefreshReg$(_c, \"TweetCenterPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/tweet-center.tsx\n"));

/***/ })

});