"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MeetingPage = ()=>{\n    // Premium color palette - matching sidebar\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            inverse: \"#FFFFFF\"\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\"\n        },\n        shadow: {\n            subtle: \"rgba(15, 23, 42, 0.04)\",\n            medium: \"rgba(15, 23, 42, 0.08)\",\n            strong: \"rgba(15, 23, 42, 0.12)\",\n            primary: \"rgba(14, 165, 233, 0.15)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: colors.surfaceElevated,\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"32px 40px\",\n                    backgroundColor: colors.surface,\n                    borderBottom: \"1px solid \".concat(colors.border.light),\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    boxShadow: \"\\n          0 0 0 1px \".concat(colors.border.light, \",\\n          0 4px 24px \").concat(colors.shadow.subtle, \"\\n        \"),\n                    position: \"relative\",\n                    zIndex: 10\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: \"\\n            radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"04 0%, transparent 50%),\\n            radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"03 0%, transparent 50%)\\n          \"),\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"32px\",\n                                    fontWeight: \"800\",\n                                    letterSpacing: \"-1px\",\n                                    lineHeight: \"1.2\"\n                                },\n                                children: \"AI Conference Suite\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: \"8px 0 0 0\",\n                                    fontSize: \"16px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"500\",\n                                    letterSpacing: \"0.1px\"\n                                },\n                                children: \"Enterprise-grade video collaboration with AI assistance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"linear-gradient(135deg, \".concat(colors.accent, \" 0%, \").concat(colors.surface, \" 100%)\"),\n                                border: \"1px solid \".concat(colors.border.primary, \"40\"),\n                                padding: \"12px 20px\",\n                                borderRadius: \"16px\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\",\n                                boxShadow: \"\\n              0 4px 16px \".concat(colors.shadow.primary, \",\\n              inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n            \")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"8px\",\n                                        height: \"8px\",\n                                        borderRadius: \"50%\",\n                                        background: \"linear-gradient(135deg, #10B981 0%, #34D399 100%)\",\n                                        boxShadow: \"0 0 8px rgba(16, 185, 129, 0.4)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        fontSize: \"15px\",\n                                        fontWeight: \"700\",\n                                        letterSpacing: \"-0.1px\"\n                                    },\n                                    children: \"Live Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: \"40px\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        backgroundColor: colors.surface,\n                        borderRadius: \"24px\",\n                        overflow: \"hidden\",\n                        boxShadow: \"\\n            0 0 0 1px \".concat(colors.border.light, \",\\n            0 8px 32px \").concat(colors.shadow.medium, \",\\n            0 24px 64px \").concat(colors.shadow.subtle, \"\\n          \"),\n                        border: \"1px solid \".concat(colors.border.light),\n                        position: \"relative\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            borderRadius: \"24px\",\n                            background: \"linear-gradient(135deg, \".concat(colors.border.primary, \"20 0%, transparent 50%, \").concat(colors.primaryLight, \"10 100%)\"),\n                            padding: \"2px\",\n                            zIndex: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\",\n                                borderRadius: \"22px\",\n                                overflow: \"hidden\",\n                                backgroundColor: colors.surface\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                src: \"https://demo.daily.co/hello\",\n                                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    border: \"none\"\n                                },\n                                title: \"Daily.co Video Meeting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"\\uD83D\\uDE80 AI Assistant integration coming soon!\\n\\nThis premium feature will allow you to:\\n• Get real-time meeting insights\\n• Generate automated summaries\\n• Ask questions during calls\\n• Access enterprise AI tools\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"48px\",\n                    right: \"48px\",\n                    background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                    color: colors.text.inverse,\n                    border: \"none\",\n                    borderRadius: \"24px\",\n                    width: \"80px\",\n                    height: \"80px\",\n                    fontSize: \"32px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"\\n            0 0 0 1px \".concat(colors.border.primary, \"40,\\n            0 12px 32px \").concat(colors.shadow.primary, \",\\n            0 24px 64px \").concat(colors.shadow.medium, \",\\n            inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n          \"),\n                    transition: \"all 0.4s cubic-bezier(0.16, 1, 0.3, 1)\",\n                    zIndex: 1000,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    fontWeight: \"600\"\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.15) translateY(-4px) rotate(5deg)\";\n                    e.currentTarget.style.boxShadow = \"\\n            0 0 0 1px \".concat(colors.border.primary, \"60,\\n            0 16px 48px \").concat(colors.shadow.primary, \",\\n            0 32px 80px \").concat(colors.shadow.medium, \",\\n            inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n          \");\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1) translateY(0) rotate(0deg)\";\n                    e.currentTarget.style.boxShadow = \"\\n            0 0 0 1px \".concat(colors.border.primary, \"40,\\n            0 12px 32px \").concat(colors.shadow.primary, \",\\n            0 24px 64px \").concat(colors.shadow.medium, \",\\n            inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n          \");\n                },\n                title: \"Exie AI Assistant - Enterprise Features\",\n                children: \"◈\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"48px\",\n                    left: \"50%\",\n                    transform: \"translateX(-50%)\",\n                    display: \"flex\",\n                    gap: \"16px\",\n                    padding: \"16px 24px\",\n                    background: \"linear-gradient(135deg, \".concat(colors.surface, \"f0 0%, \").concat(colors.accent, \"f0 100%)\"),\n                    borderRadius: \"20px\",\n                    border: \"1px solid \".concat(colors.border.light),\n                    boxShadow: \"\\n          0 0 0 1px \".concat(colors.border.primary, \"20,\\n          0 8px 32px \").concat(colors.shadow.medium, \",\\n          inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n        \"),\n                    backdropFilter: \"blur(20px)\",\n                    zIndex: 999\n                },\n                children: [\n                    \"◐\",\n                    \"◑\",\n                    \"◒\"\n                ].map((icon, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        style: {\n                            width: \"48px\",\n                            height: \"48px\",\n                            borderRadius: \"12px\",\n                            border: \"1px solid \".concat(colors.border.light),\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.surfaceElevated, \" 100%)\"),\n                            color: colors.text.secondary,\n                            fontSize: \"20px\",\n                            cursor: \"pointer\",\n                            transition: \"all 0.3s ease\",\n                            boxShadow: \"\\n                0 2px 8px \".concat(colors.shadow.subtle, \",\\n                inset 0 1px 0 rgba(255, 255, 255, 0.6)\\n              \"),\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\"\n                        },\n                        onMouseOver: (e)=>{\n                            e.currentTarget.style.transform = \"scale(1.1)\";\n                            e.currentTarget.style.color = colors.primary;\n                        },\n                        onMouseOut: (e)=>{\n                            e.currentTarget.style.transform = \"scale(1)\";\n                            e.currentTarget.style.color = colors.text.secondary;\n                        },\n                        children: icon\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});