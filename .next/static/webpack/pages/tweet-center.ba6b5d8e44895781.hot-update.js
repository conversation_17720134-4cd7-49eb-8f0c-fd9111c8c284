"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Dashboard\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Insights\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"Content\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: \"\\n        radial-gradient(circle at 20% 20%, \".concat(colors.warmGlow, \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 80%, \").concat(colors.accent, \" 0%, transparent 50%),\\n        linear-gradient(135deg, \").concat(colors.background, \" 0%, \").concat(colors.accent, \" 100%)\\n      \"),\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"20px\",\n                    boxShadow: \"\\n          0 20px 60px \".concat(colors.sidebar.glow, \",\\n          0 8px 32px rgba(0, 0, 0, 0.15),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n        \"),\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border),\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            position: \"relative\",\n                            background: \"\\n            radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\\n          \"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"50%\",\n                                    left: \"50%\",\n                                    transform: \"translate(-50%, -50%)\",\n                                    width: \"80px\",\n                                    height: \"80px\",\n                                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)\",\n                                    borderRadius: \"50%\",\n                                    filter: \"blur(20px)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    width: \"60px\",\n                                    height: \"60px\",\n                                    borderRadius: \"18px\",\n                                    background: \"\\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n            \",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"\\n              0 8px 32px rgba(0, 0, 0, 0.1),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n            \"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"28px\",\n                                        color: colors.sidebar.text,\n                                        fontWeight: \"400\",\n                                        fontFamily: \"Georgia, serif\",\n                                        fontStyle: \"italic\",\n                                        textShadow: \"0 2px 8px rgba(0, 0, 0, 0.2)\",\n                                        letterSpacing: \"-1px\"\n                                    },\n                                    children: \"ℰ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"16px\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? \"\\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                        \" : hovered ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                          \" : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"\\n                          0 8px 32px rgba(0, 0, 0, 0.1),\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n                        \" : hovered ? \"\\n                            0 4px 16px rgba(0, 0, 0, 0.05),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n                          \" : \"none\",\n                                                transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-2px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        background: \"\\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\\n                          \",\n                                                        borderRadius: \"0 8px 8px 0\",\n                                                        boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"32px\",\n                                                        height: \"32px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"10px\",\n                                                        background: active ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                          \" : hovered ? \"\\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                            \" : \"transparent\",\n                                                        transition: \"all 0.3s ease\",\n                                                        transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 18,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            transition: \"all 0.3s ease\",\n                                                            filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        transition: \"all 0.3s ease\",\n                                                        letterSpacing: \"-0.2px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && !active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        right: \"12px\",\n                                                        width: \"6px\",\n                                                        height: \"6px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"\\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\\n                          \",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                padding: \"8px\",\n                                backgroundColor: colors.sidebar.hover,\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: colors.sidebar.backgroundLight,\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-1px\",\n                                                right: \"-1px\",\n                                                width: \"8px\",\n                                                height: \"8px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#10B981\",\n                                                border: \"1px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                color: colors.sidebar.text,\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"1px\"\n                                            },\n                                            children: \"Alex Chen\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"11px\",\n                                                color: colors.sidebar.textTertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            color: colors.sidebar.textSecondary\n                                        },\n                                        children: \"⌄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surface,\n                        borderRadius: \"12px\",\n                        minHeight: \"calc(100vh - 32px)\",\n                        boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.06)\",\n                        overflow: \"hidden\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});