"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, #FFE0B220 100%)\"),\n                    borderRadius: \"24px\",\n                    padding: \"32px\",\n                    marginBottom: \"32px\",\n                    boxShadow: \"\\n          0 20px 60px rgba(0, 0, 0, 0.08),\\n          0 8px 32px rgba(0, 0, 0, 0.04),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.9)\\n        \",\n                    border: \"1px solid \".concat(colors.border),\n                    position: \"relative\",\n                    overflow: \"hidden\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"100px\",\n                            background: \"radial-gradient(ellipse at top, #FFE0B230 0%, transparent 70%)\",\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            borderRadius: \"10px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \"20 0%, #FF8A6520 100%)\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Today's Mission\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"4px 12px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #FF8A65 100%)\"),\n                                            borderRadius: \"12px\",\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Generated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.text.secondary,\n                                    fontSize: \"18px\",\n                                    lineHeight: \"1.6\",\n                                    margin: 0,\n                                    marginBottom: \"24px\"\n                                },\n                                children: '\"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(3, 1fr)\",\n                                    gap: \"20px\",\n                                    marginBottom: \"24px\"\n                                },\n                                children: [\n                                    {\n                                        label: \"Engagement Rate\",\n                                        value: \"+24%\",\n                                        icon: \"\\uD83D\\uDCC8\"\n                                    },\n                                    {\n                                        label: \"New Followers\",\n                                        value: \"127\",\n                                        icon: \"\\uD83D\\uDC65\"\n                                    },\n                                    {\n                                        label: \"Content Score\",\n                                        value: \"8.9/10\",\n                                        icon: \"⭐\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\",\n                                            borderRadius: \"16px\",\n                                            padding: \"20px\",\n                                            textAlign: \"center\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"700\",\n                                                    color: colors.text.primary,\n                                                    marginBottom: \"4px\"\n                                                },\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"16px\",\n                                    flexWrap: \"wrap\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/meeting\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #FF8A65 100%)\"),\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                boxShadow: \"0 8px 24px \".concat(colors.primary, \"40\"),\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"\\uD83C\\uDFA5 Join Call\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"16px 24px\",\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)\",\n                                            color: colors.text.primary,\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"16px\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            backdropFilter: \"blur(10px)\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        children: \"\\uD83E\\uDD16 Ask Mentor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/tweet-center\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)\",\n                                                color: colors.text.primary,\n                                                border: \"1px solid \".concat(colors.border),\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                backdropFilter: \"blur(10px)\",\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"✍️ Generate Tweet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, #FFE0B215 100%)\"),\n                    borderRadius: \"20px\",\n                    padding: \"24px\",\n                    boxShadow: \"\\n          0 12px 40px rgba(0, 0, 0, 0.06),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n        \",\n                    border: \"1px solid \".concat(colors.border)\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"64px\",\n                                height: \"64px\",\n                                borderRadius: \"20px\",\n                                background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #FF8A65 100%)\"),\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: \"0 8px 24px \".concat(colors.primary, \"30\"),\n                                position: \"relative\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"32px\"\n                                    },\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"absolute\",\n                                        bottom: \"-2px\",\n                                        right: \"-2px\",\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        borderRadius: \"50%\",\n                                        background: \"#00E676\",\n                                        border: \"2px solid white\",\n                                        boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"18px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"4px\"\n                                    },\n                                    children: \"AI Mentor\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: '\"Ready to help you create content that resonates. What\\'s on your mind today?\"'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});