"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Minimal Orange theme - Professional & Clean\n    const colors = {\n        primary: \"#F97316\",\n        primaryLight: \"#FB923C\",\n        primaryDark: \"#EA580C\",\n        accent: \"#FFF7ED\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FEFEFE\",\n        background: \"#F8F9FA\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\",\n            primary: \"#F97316\" // Orange border\n        },\n        sidebar: {\n            background: \"#F97316\",\n            backgroundLight: \"#FB923C\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.8)\",\n            textTertiary: \"rgba(255, 255, 255, 0.6)\",\n            hover: \"rgba(255, 255, 255, 0.1)\",\n            active: \"rgba(255, 255, 255, 0.15)\",\n            border: \"rgba(255, 255, 255, 0.1)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: \"□\",\n            description: \"Dashboard\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Analytics\",\n            icon: \"▢\",\n            description: \"Insights\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social\",\n            icon: \"◇\",\n            description: \"Content\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Meetings\",\n            icon: \"○\",\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.primary\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"220px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"2px 0 8px rgba(0, 0, 0, 0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"10px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"24px\",\n                                            color: colors.sidebar.text,\n                                            fontWeight: \"400\",\n                                            fontFamily: \"Georgia, serif\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"ℰ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: colors.sidebar.text,\n                                        letterSpacing: \"-0.3px\"\n                                    },\n                                    children: \"Exie\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"12px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 12px\"\n                            },\n                            children: menuItems.map((item)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"4px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"8px 12px\",\n                                                borderRadius: \"8px\",\n                                                transition: \"all 0.2s ease\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                backgroundColor: active ? colors.sidebar.active : hovered ? colors.sidebar.hover : \"transparent\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"16px\",\n                                                        background: colors.sidebar.backgroundLight,\n                                                        borderRadius: \"0 2px 2px 0\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"10px\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                            transition: \"color 0.2s ease\"\n                                                        },\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: active ? colors.sidebar.text : colors.sidebar.textSecondary,\n                                                        transition: \"all 0.2s ease\",\n                                                        letterSpacing: \"-0.1px\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"12px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                padding: \"8px\",\n                                backgroundColor: colors.sidebar.hover,\n                                borderRadius: \"8px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.2s ease\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"32px\",\n                                        height: \"32px\",\n                                        background: colors.sidebar.backgroundLight,\n                                        borderRadius: \"8px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-1px\",\n                                                right: \"-1px\",\n                                                width: \"8px\",\n                                                height: \"8px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#10B981\",\n                                                border: \"1px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"13px\",\n                                                fontWeight: \"600\",\n                                                color: colors.sidebar.text,\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"1px\"\n                                            },\n                                            children: \"Alex Chen\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"11px\",\n                                                color: colors.sidebar.textTertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"10px\",\n                                            color: colors.sidebar.textSecondary\n                                        },\n                                        children: \"⌄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.primary,\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    padding: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surface,\n                        borderRadius: \"12px\",\n                        minHeight: \"calc(100vh - 32px)\",\n                        boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.06)\",\n                        overflow: \"hidden\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1NpZGViYXJMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ1c7QUFDQTtBQU14QyxNQUFNSSxnQkFBOEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQy9ELE1BQU1DLFNBQVNILHNEQUFTQTtJQUN4QixNQUFNLENBQUNJLGFBQWFDLGVBQWUsR0FBR04sK0NBQVFBLENBQWdCO0lBRTlELDhDQUE4QztJQUM5QyxNQUFNTyxTQUFTO1FBQ2JDLFNBQVM7UUFDVEMsY0FBYztRQUNkQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxpQkFBaUI7UUFDakJDLFlBQVk7UUFDWkMsTUFBTTtZQUNKUCxTQUFTO1lBQ1RRLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLFNBQVMsVUFBZ0IsYUFBYTtRQUN4QztRQUNBQyxRQUFRO1lBQ05DLE9BQU87WUFDUEMsUUFBUTtZQUNSZCxTQUFTLFVBQWdCLGdCQUFnQjtRQUMzQztRQUNBZSxTQUFTO1lBQ1BULFlBQVk7WUFDWlUsaUJBQWlCO1lBQ2pCVCxNQUFNO1lBQ05VLGVBQWU7WUFDZkMsY0FBYztZQUNkQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUlIsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNUyxZQUFZO1FBQ2hCO1lBQ0VDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtLQUNEO0lBRUQsTUFBTUMsV0FBVyxDQUFDSjtRQUNoQixJQUFJQSxTQUFTLEtBQUs7WUFDaEIsT0FBTzFCLE9BQU8rQixRQUFRLEtBQUs7UUFDN0I7UUFDQSxPQUFPL0IsT0FBTytCLFFBQVEsQ0FBQ0MsVUFBVSxDQUFDTjtJQUNwQztJQUVBLHFCQUNFLDhEQUFDTztRQUFJQyxPQUFPO1lBQUVDLFNBQVM7WUFBUUMsV0FBVztZQUFTQyxpQkFBaUJsQyxPQUFPQyxPQUFPO1FBQUM7OzBCQUVqRiw4REFBQ2tDO2dCQUFNSixPQUFPO29CQUNaSyxPQUFPO29CQUNQN0IsWUFBWVAsT0FBT2dCLE9BQU8sQ0FBQ1QsVUFBVTtvQkFDckMwQixXQUFXO29CQUNYSSxVQUFVO29CQUNWTCxTQUFTO29CQUNUTSxlQUFlO29CQUNmQyxXQUFXO2dCQUNiOztrQ0FFRSw4REFBQ1Q7d0JBQUlDLE9BQU87NEJBQ1ZTLFNBQVM7NEJBQ1RDLGNBQWMsYUFBbUMsT0FBdEJ6QyxPQUFPZ0IsT0FBTyxDQUFDSCxNQUFNO3dCQUNsRDtrQ0FDRSw0RUFBQ2lCOzRCQUFJQyxPQUFPO2dDQUFFQyxTQUFTO2dDQUFRVSxZQUFZO2dDQUFVQyxLQUFLOzRCQUFPOzs4Q0FDL0QsOERBQUNiO29DQUFJQyxPQUFPO3dDQUNWQyxTQUFTO3dDQUNUVSxZQUFZO3dDQUNaRSxnQkFBZ0I7b0NBQ2xCOzhDQUNFLDRFQUFDQzt3Q0FBS2QsT0FBTzs0Q0FDWGUsVUFBVTs0Q0FDVkMsT0FBTy9DLE9BQU9nQixPQUFPLENBQUNSLElBQUk7NENBQzFCd0MsWUFBWTs0Q0FDWkMsWUFBWTs0Q0FDWkMsV0FBVzt3Q0FDYjtrREFBRzs7Ozs7Ozs7Ozs7OENBSUwsOERBQUNDO29DQUFHcEIsT0FBTzt3Q0FDVHFCLFFBQVE7d0NBQ1JOLFVBQVU7d0NBQ1ZFLFlBQVk7d0NBQ1pELE9BQU8vQyxPQUFPZ0IsT0FBTyxDQUFDUixJQUFJO3dDQUMxQjZDLGVBQWU7b0NBQ2pCOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPUCw4REFBQ0M7d0JBQUl2QixPQUFPOzRCQUFFd0IsTUFBTTs0QkFBR2YsU0FBUzs0QkFBVWdCLFVBQVU7d0JBQU87a0NBQ3pELDRFQUFDMUI7NEJBQUlDLE9BQU87Z0NBQUVTLFNBQVM7NEJBQVM7c0NBQzdCbEIsVUFBVW1DLEdBQUcsQ0FBQyxDQUFDQztnQ0FDZCxNQUFNckMsU0FBU00sU0FBUytCLEtBQUtuQyxJQUFJO2dDQUNqQyxNQUFNb0MsVUFBVTdELGdCQUFnQjRELEtBQUtuQyxJQUFJO2dDQUV6QyxxQkFDRSw4REFBQ087b0NBQW9CQyxPQUFPO3dDQUFFNkIsY0FBYztvQ0FBTTs4Q0FDaEQsNEVBQUNyRSxrREFBSUE7d0NBQ0hnQyxNQUFNbUMsS0FBS25DLElBQUk7d0NBQ2ZRLE9BQU87NENBQUU4QixnQkFBZ0I7d0NBQU87d0NBQ2hDQyxjQUFjLElBQU0vRCxlQUFlMkQsS0FBS25DLElBQUk7d0NBQzVDd0MsY0FBYyxJQUFNaEUsZUFBZTtrREFFbkMsNEVBQUMrQjs0Q0FBSUMsT0FBTztnREFDVkMsU0FBUztnREFDVFUsWUFBWTtnREFDWkYsU0FBUztnREFDVHdCLGNBQWM7Z0RBQ2RDLFlBQVk7Z0RBQ1pDLFFBQVE7Z0RBQ1I3QixVQUFVO2dEQUNWSCxpQkFBaUJiLFNBQ2JyQixPQUFPZ0IsT0FBTyxDQUFDSyxNQUFNLEdBQ3JCc0MsVUFDRTNELE9BQU9nQixPQUFPLENBQUNJLEtBQUssR0FDcEI7NENBQ1I7O2dEQUVHQyx3QkFDQyw4REFBQ1M7b0RBQUlDLE9BQU87d0RBQ1ZNLFVBQVU7d0RBQ1Y4QixNQUFNO3dEQUNOQyxLQUFLO3dEQUNMQyxXQUFXO3dEQUNYakMsT0FBTzt3REFDUGtDLFFBQVE7d0RBQ1IvRCxZQUFZUCxPQUFPZ0IsT0FBTyxDQUFDQyxlQUFlO3dEQUMxQytDLGNBQWM7b0RBQ2hCOzs7Ozs7OERBSUYsOERBQUNsQztvREFBSUMsT0FBTzt3REFDVkssT0FBTzt3REFDUGtDLFFBQVE7d0RBQ1J0QyxTQUFTO3dEQUNUVSxZQUFZO3dEQUNaRSxnQkFBZ0I7d0RBQ2hCMkIsYUFBYTtvREFDZjs4REFDRSw0RUFBQzFCO3dEQUFLZCxPQUFPOzREQUNYZSxVQUFVOzREQUNWQyxPQUFPMUIsU0FBU3JCLE9BQU9nQixPQUFPLENBQUNSLElBQUksR0FBR1IsT0FBT2dCLE9BQU8sQ0FBQ0UsYUFBYTs0REFDbEUrQyxZQUFZO3dEQUNkO2tFQUNHUCxLQUFLakMsSUFBSTs7Ozs7Ozs7Ozs7OERBS2QsOERBQUNLO29EQUFJQyxPQUFPO3dEQUNWZSxVQUFVO3dEQUNWRSxZQUFZM0IsU0FBUyxRQUFRO3dEQUM3QjBCLE9BQU8xQixTQUFTckIsT0FBT2dCLE9BQU8sQ0FBQ1IsSUFBSSxHQUFHUixPQUFPZ0IsT0FBTyxDQUFDRSxhQUFhO3dEQUNsRStDLFlBQVk7d0RBQ1paLGVBQWU7b0RBQ2pCOzhEQUNHSyxLQUFLbEMsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBN0RUa0MsS0FBS25DLElBQUk7Ozs7OzRCQW1FdkI7Ozs7Ozs7Ozs7O2tDQUtKLDhEQUFDTzt3QkFBSUMsT0FBTzs0QkFDVlMsU0FBUzs0QkFDVGdDLFdBQVcsYUFBbUMsT0FBdEJ4RSxPQUFPZ0IsT0FBTyxDQUFDSCxNQUFNOzRCQUM3QzRELFdBQVc7d0JBQ2I7a0NBR0UsNEVBQUMzQzs0QkFBSUMsT0FBTztnQ0FDVkMsU0FBUztnQ0FDVFUsWUFBWTtnQ0FDWkMsS0FBSztnQ0FDTEgsU0FBUztnQ0FDVE4saUJBQWlCbEMsT0FBT2dCLE9BQU8sQ0FBQ0ksS0FBSztnQ0FDckM0QyxjQUFjO2dDQUNkRSxRQUFRO2dDQUNSRCxZQUFZOzRCQUNkOzs4Q0FFRSw4REFBQ25DO29DQUFJQyxPQUFPO3dDQUNWSyxPQUFPO3dDQUNQa0MsUUFBUTt3Q0FDUi9ELFlBQVlQLE9BQU9nQixPQUFPLENBQUNDLGVBQWU7d0NBQzFDK0MsY0FBYzt3Q0FDZGhDLFNBQVM7d0NBQ1RVLFlBQVk7d0NBQ1pFLGdCQUFnQjt3Q0FDaEJQLFVBQVU7b0NBQ1o7O3NEQUNFLDhEQUFDUTs0Q0FBS2QsT0FBTztnREFDWGdCLE9BQU8vQyxPQUFPZ0IsT0FBTyxDQUFDUixJQUFJO2dEQUMxQnNDLFVBQVU7Z0RBQ1ZFLFlBQVk7NENBQ2Q7c0RBQUc7Ozs7OztzREFJSCw4REFBQ2xCOzRDQUFJQyxPQUFPO2dEQUNWTSxVQUFVO2dEQUNWcUMsUUFBUTtnREFDUkMsT0FBTztnREFDUHZDLE9BQU87Z0RBQ1BrQyxRQUFRO2dEQUNSTixjQUFjO2dEQUNkekQsWUFBWTtnREFDWk0sUUFBUTs0Q0FDVjs7Ozs7Ozs7Ozs7OzhDQUlGLDhEQUFDaUI7b0NBQUlDLE9BQU87d0NBQUV3QixNQUFNO29DQUFFOztzREFDcEIsOERBQUN6Qjs0Q0FBSUMsT0FBTztnREFDVmUsVUFBVTtnREFDVkUsWUFBWTtnREFDWkQsT0FBTy9DLE9BQU9nQixPQUFPLENBQUNSLElBQUk7Z0RBQzFCb0UsWUFBWTtnREFDWmhCLGNBQWM7NENBQ2hCO3NEQUFHOzs7Ozs7c0RBR0gsOERBQUM5Qjs0Q0FBSUMsT0FBTztnREFDVmUsVUFBVTtnREFDVkMsT0FBTy9DLE9BQU9nQixPQUFPLENBQUNHLFlBQVk7Z0RBQ2xDeUQsWUFBWTs0Q0FDZDtzREFBRzs7Ozs7Ozs7Ozs7OzhDQU1MLDhEQUFDOUM7b0NBQUlDLE9BQU87d0NBQ1ZLLE9BQU87d0NBQ1BrQyxRQUFRO3dDQUNSdEMsU0FBUzt3Q0FDVFUsWUFBWTt3Q0FDWkUsZ0JBQWdCO29DQUNsQjs4Q0FDRSw0RUFBQ0M7d0NBQUtkLE9BQU87NENBQ1hlLFVBQVU7NENBQ1ZDLE9BQU8vQyxPQUFPZ0IsT0FBTyxDQUFDRSxhQUFhO3dDQUNyQztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTWCw4REFBQzJEO2dCQUFLOUMsT0FBTztvQkFDWCtDLFVBQVU7b0JBQ1Y1QyxpQkFBaUJsQyxPQUFPQyxPQUFPO29CQUMvQmdDLFdBQVc7b0JBQ1hJLFVBQVU7b0JBQ1ZHLFNBQVM7Z0JBQ1g7MEJBQ0UsNEVBQUNWO29CQUFJQyxPQUFPO3dCQUNWRyxpQkFBaUJsQyxPQUFPSyxPQUFPO3dCQUMvQjJELGNBQWM7d0JBQ2QvQixXQUFXO3dCQUNYTSxXQUFXO3dCQUNYaUIsVUFBVTtvQkFDWjs4QkFDRzVEOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYO0dBL1NNRDs7UUFDV0Qsa0RBQVNBOzs7S0FEcEJDO0FBaVROLCtEQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvU2lkZWJhckxheW91dC50c3g/NTEzZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuXG5pbnRlcmZhY2UgU2lkZWJhckxheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgU2lkZWJhckxheW91dDogUmVhY3QuRkM8U2lkZWJhckxheW91dFByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtob3ZlcmVkSXRlbSwgc2V0SG92ZXJlZEl0ZW1dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gTWluaW1hbCBPcmFuZ2UgdGhlbWUgLSBQcm9mZXNzaW9uYWwgJiBDbGVhblxuICBjb25zdCBjb2xvcnMgPSB7XG4gICAgcHJpbWFyeTogJyNGOTczMTYnLCAgICAgICAgLy8gT3JhbmdlXG4gICAgcHJpbWFyeUxpZ2h0OiAnI0ZCOTIzQycsICAgLy8gTGlnaHQgb3JhbmdlXG4gICAgcHJpbWFyeURhcms6ICcjRUE1ODBDJywgICAgLy8gRGFyayBvcmFuZ2VcbiAgICBhY2NlbnQ6ICcjRkZGN0VEJywgICAgICAgICAvLyBWZXJ5IGxpZ2h0IG9yYW5nZVxuICAgIHN1cmZhY2U6ICcjRkZGRkZGJywgICAgICAgIC8vIFB1cmUgd2hpdGVcbiAgICBzdXJmYWNlRWxldmF0ZWQ6ICcjRkVGRUZFJywgLy8gU2xpZ2h0bHkgb2ZmLXdoaXRlXG4gICAgYmFja2dyb3VuZDogJyNGOEY5RkEnLCAgICAgLy8gTmV1dHJhbCBsaWdodCBiYWNrZ3JvdW5kXG4gICAgdGV4dDoge1xuICAgICAgcHJpbWFyeTogJyMxRjI5MzcnLCAgICAgIC8vIERhcmsgZ3JheVxuICAgICAgc2Vjb25kYXJ5OiAnIzZCNzI4MCcsICAgIC8vIE1lZGl1bSBncmF5XG4gICAgICB0ZXJ0aWFyeTogJyM5Q0EzQUYnLCAgICAgLy8gTGlnaHQgZ3JheVxuICAgICAgbXV0ZWQ6ICcjRDFENURCJywgICAgICAgIC8vIFZlcnkgbGlnaHQgZ3JheVxuICAgICAgaW52ZXJzZTogJyNGRkZGRkYnICAgICAgIC8vIFdoaXRlIHRleHRcbiAgICB9LFxuICAgIGJvcmRlcjoge1xuICAgICAgbGlnaHQ6ICcjRTVFN0VCJywgICAgICAgIC8vIExpZ2h0IGJvcmRlclxuICAgICAgbWVkaXVtOiAnI0QxRDVEQicsICAgICAgIC8vIE1lZGl1bSBib3JkZXJcbiAgICAgIHByaW1hcnk6ICcjRjk3MzE2JyAgICAgICAvLyBPcmFuZ2UgYm9yZGVyXG4gICAgfSxcbiAgICBzaWRlYmFyOiB7XG4gICAgICBiYWNrZ3JvdW5kOiAnI0Y5NzMxNicsICAgLy8gT3JhbmdlIHNpZGViYXJcbiAgICAgIGJhY2tncm91bmRMaWdodDogJyNGQjkyM0MnLFxuICAgICAgdGV4dDogJyNGRkZGRkYnLFxuICAgICAgdGV4dFNlY29uZGFyeTogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC44KScsXG4gICAgICB0ZXh0VGVydGlhcnk6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNiknLFxuICAgICAgaG92ZXI6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLFxuICAgICAgYWN0aXZlOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KScsXG4gICAgICBib3JkZXI6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknXG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IG1lbnVJdGVtcyA9IFtcbiAgICB7XG4gICAgICBocmVmOiAnLycsXG4gICAgICBsYWJlbDogJ092ZXJ2aWV3JyxcbiAgICAgIGljb246ICfilqEnLFxuICAgICAgZGVzY3JpcHRpb246ICdEYXNoYm9hcmQnXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiAnL2Rhc2hib2FyZCcsXG4gICAgICBsYWJlbDogJ0FuYWx5dGljcycsXG4gICAgICBpY29uOiAn4paiJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnSW5zaWdodHMnXG4gICAgfSxcbiAgICB7XG4gICAgICBocmVmOiAnL3R3ZWV0LWNlbnRlcicsXG4gICAgICBsYWJlbDogJ1NvY2lhbCcsXG4gICAgICBpY29uOiAn4peHJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29udGVudCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGhyZWY6ICcvbWVldGluZycsXG4gICAgICBsYWJlbDogJ01lZXRpbmdzJyxcbiAgICAgIGljb246ICfil4snLFxuICAgICAgZGVzY3JpcHRpb246ICdWaWRlbyBDYWxscydcbiAgICB9XG4gIF07XG5cbiAgY29uc3QgaXNBY3RpdmUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGhyZWYgPT09ICcvJykge1xuICAgICAgcmV0dXJuIHJvdXRlci5wYXRobmFtZSA9PT0gJy8nO1xuICAgIH1cbiAgICByZXR1cm4gcm91dGVyLnBhdGhuYW1lLnN0YXJ0c1dpdGgoaHJlZik7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgbWluSGVpZ2h0OiAnMTAwdmgnLCBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5wcmltYXJ5IH19PlxuICAgICAgey8qIE1pbmltYWwgT3JhbmdlIFNpZGViYXIgKi99XG4gICAgICA8YXNpZGUgc3R5bGU9e3tcbiAgICAgICAgd2lkdGg6ICcyMjBweCcsXG4gICAgICAgIGJhY2tncm91bmQ6IGNvbG9ycy5zaWRlYmFyLmJhY2tncm91bmQsXG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICAgIGJveFNoYWRvdzogJzJweCAwIDhweCByZ2JhKDAsIDAsIDAsIDAuMSknXG4gICAgICB9fT5cbiAgICAgICAgey8qIENvbXBhY3QgSGVhZGVyICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzE2cHgnLFxuICAgICAgICAgIGJvcmRlckJvdHRvbTogYDFweCBzb2xpZCAke2NvbG9ycy5zaWRlYmFyLmJvcmRlcn1gXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTBweCcgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcyNHB4JyxcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dCxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNDAwJyxcbiAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAnR2VvcmdpYSwgc2VyaWYnLFxuICAgICAgICAgICAgICAgIGZvbnRTdHlsZTogJ2l0YWxpYydcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg4oSwXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgxIHN0eWxlPXt7XG4gICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxNnB4JyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0LFxuICAgICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiAnLTAuM3B4J1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIEV4aWVcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNaW5pbWFsIE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxuYXYgc3R5bGU9e3sgZmxleDogMSwgcGFkZGluZzogJzEycHggMCcsIG92ZXJmbG93OiAnYXV0bycgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nOiAnMCAxMnB4JyB9fT5cbiAgICAgICAgICAgIHttZW51SXRlbXMubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGFjdGl2ZSA9IGlzQWN0aXZlKGl0ZW0uaHJlZik7XG4gICAgICAgICAgICAgIGNvbnN0IGhvdmVyZWQgPSBob3ZlcmVkSXRlbSA9PT0gaXRlbS5ocmVmO1xuXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0uaHJlZn0gc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnNHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdGV4dERlY29yYXRpb246ICdub25lJyB9fVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEhvdmVyZWRJdGVtKGl0ZW0uaHJlZil9XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0SG92ZXJlZEl0ZW0obnVsbCl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzhweCAxMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBjb2xvcnMuc2lkZWJhci5hY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogaG92ZXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGNvbG9ycy5zaWRlYmFyLmhvdmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RyYW5zcGFyZW50J1xuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQWN0aXZlIGluZGljYXRvciAqL31cbiAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6ICcwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdG9wOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtNTAlKScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnM3B4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGNvbG9ycy5zaWRlYmFyLmJhY2tncm91bmRMaWdodCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMCAycHggMnB4IDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogSWNvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzIwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMjBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTBweCdcbiAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBhY3RpdmUgPyBjb2xvcnMuc2lkZWJhci50ZXh0IDogY29sb3JzLnNpZGViYXIudGV4dFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2NvbG9yIDAuMnMgZWFzZSdcbiAgICAgICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pY29ufVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIExhYmVsICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBhY3RpdmUgPyAnNjAwJyA6ICc1MDAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGFjdGl2ZSA/IGNvbG9ycy5zaWRlYmFyLnRleHQgOiBjb2xvcnMuc2lkZWJhci50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxuICAgICAgICAgICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJy0wLjFweCdcbiAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L25hdj5cblxuICAgICAgICB7LyogQ29tcGFjdCBBY2NvdW50IFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnMTJweCcsXG4gICAgICAgICAgYm9yZGVyVG9wOiBgMXB4IHNvbGlkICR7Y29sb3JzLnNpZGViYXIuYm9yZGVyfWAsXG4gICAgICAgICAgbWFyZ2luVG9wOiAnYXV0bydcbiAgICAgICAgfX0+XG5cbiAgICAgICAgICB7LyogTWluaW1hbCBBY2NvdW50IFByb2ZpbGUgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICBnYXA6ICc4cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzhweCcsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zaWRlYmFyLmhvdmVyLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7LyogQXZhdGFyICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICB3aWR0aDogJzMycHgnLFxuICAgICAgICAgICAgICBoZWlnaHQ6ICczMnB4JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogY29sb3JzLnNpZGViYXIuYmFja2dyb3VuZExpZ2h0LFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE0cHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIEFcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICB7LyogT25saW5lIGluZGljYXRvciAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgICAgIGJvdHRvbTogJy0xcHgnLFxuICAgICAgICAgICAgICAgIHJpZ2h0OiAnLTFweCcsXG4gICAgICAgICAgICAgICAgd2lkdGg6ICc4cHgnLFxuICAgICAgICAgICAgICAgIGhlaWdodDogJzhweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzEwQjk4MScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHdoaXRlJ1xuICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBVc2VyIEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEzcHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuc2lkZWJhci50ZXh0LFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjInLFxuICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzFweCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgQWxleCBDaGVuXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxMXB4JyxcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dFRlcnRpYXJ5LFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjInXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIE1hbmFnZXJcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIERyb3Bkb3duIGluZGljYXRvciAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICcxNnB4JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnMTZweCcsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnNpZGViYXIudGV4dFNlY29uZGFyeVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDijIRcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cblxuICAgICAgey8qIE1haW4gQ29udGVudCB3aXRoIEVsZXZhdGVkIFBhZ2UgRWZmZWN0ICovfVxuICAgICAgPG1haW4gc3R5bGU9e3tcbiAgICAgICAgZmxleEdyb3c6IDEsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgIHBhZGRpbmc6ICcxNnB4J1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zdXJmYWNlLFxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzEycHgnLFxuICAgICAgICAgIG1pbkhlaWdodDogJ2NhbGMoMTAwdmggLSAzMnB4KScsXG4gICAgICAgICAgYm94U2hhZG93OiAnMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMTIpLCAwIDAgMCAxcHggcmdiYSgwLCAwLCAwLCAwLjA2KScsXG4gICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gICAgICAgIH19PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTaWRlYmFyTGF5b3V0OyJdLCJuYW1lcyI6WyJMaW5rIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIlNpZGViYXJMYXlvdXQiLCJjaGlsZHJlbiIsInJvdXRlciIsImhvdmVyZWRJdGVtIiwic2V0SG92ZXJlZEl0ZW0iLCJjb2xvcnMiLCJwcmltYXJ5IiwicHJpbWFyeUxpZ2h0IiwicHJpbWFyeURhcmsiLCJhY2NlbnQiLCJzdXJmYWNlIiwic3VyZmFjZUVsZXZhdGVkIiwiYmFja2dyb3VuZCIsInRleHQiLCJzZWNvbmRhcnkiLCJ0ZXJ0aWFyeSIsIm11dGVkIiwiaW52ZXJzZSIsImJvcmRlciIsImxpZ2h0IiwibWVkaXVtIiwic2lkZWJhciIsImJhY2tncm91bmRMaWdodCIsInRleHRTZWNvbmRhcnkiLCJ0ZXh0VGVydGlhcnkiLCJob3ZlciIsImFjdGl2ZSIsIm1lbnVJdGVtcyIsImhyZWYiLCJsYWJlbCIsImljb24iLCJkZXNjcmlwdGlvbiIsImlzQWN0aXZlIiwicGF0aG5hbWUiLCJzdGFydHNXaXRoIiwiZGl2Iiwic3R5bGUiLCJkaXNwbGF5IiwibWluSGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwiYXNpZGUiLCJ3aWR0aCIsInBvc2l0aW9uIiwiZmxleERpcmVjdGlvbiIsImJveFNoYWRvdyIsInBhZGRpbmciLCJib3JkZXJCb3R0b20iLCJhbGlnbkl0ZW1zIiwiZ2FwIiwianVzdGlmeUNvbnRlbnQiLCJzcGFuIiwiZm9udFNpemUiLCJjb2xvciIsImZvbnRXZWlnaHQiLCJmb250RmFtaWx5IiwiZm9udFN0eWxlIiwiaDEiLCJtYXJnaW4iLCJsZXR0ZXJTcGFjaW5nIiwibmF2IiwiZmxleCIsIm92ZXJmbG93IiwibWFwIiwiaXRlbSIsImhvdmVyZWQiLCJtYXJnaW5Cb3R0b20iLCJ0ZXh0RGVjb3JhdGlvbiIsIm9uTW91c2VFbnRlciIsIm9uTW91c2VMZWF2ZSIsImJvcmRlclJhZGl1cyIsInRyYW5zaXRpb24iLCJjdXJzb3IiLCJsZWZ0IiwidG9wIiwidHJhbnNmb3JtIiwiaGVpZ2h0IiwibWFyZ2luUmlnaHQiLCJib3JkZXJUb3AiLCJtYXJnaW5Ub3AiLCJib3R0b20iLCJyaWdodCIsImxpbmVIZWlnaHQiLCJtYWluIiwiZmxleEdyb3ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});