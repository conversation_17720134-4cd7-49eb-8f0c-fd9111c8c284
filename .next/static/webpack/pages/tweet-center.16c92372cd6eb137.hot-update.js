"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: \"\\n        radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"15 0%, transparent 50%),\\n        radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"10 0%, transparent 50%),\\n        linear-gradient(135deg, \").concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\\n      \"),\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: isCollapsed ? \"60px\" : \"180px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"20px\",\n                    boxShadow: \"\\n          0 20px 60px \".concat(colors.sidebar.glow, \",\\n          0 8px 32px rgba(0, 0, 0, 0.15),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n        \"),\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                    transition: \"width 0.3s cubic-bezier(0.4, 0, 0.2, 1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"20px 8px\" : \"24px 16px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border),\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            position: \"relative\",\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: isCollapsed ? \"28px\" : \"36px\",\n                                    color: colors.sidebar.text,\n                                    fontWeight: \"400\",\n                                    fontFamily: \"Georgia, serif\",\n                                    fontStyle: \"italic\",\n                                    textShadow: \"0 4px 12px rgba(0, 0, 0, 0.3)\",\n                                    letterSpacing: \"-2px\",\n                                    transition: \"font-size 0.3s ease\"\n                                },\n                                children: \"ℰ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                style: {\n                                    position: \"absolute\",\n                                    right: isCollapsed ? \"6px\" : \"8px\",\n                                    top: \"50%\",\n                                    transform: \"translateY(-50%)\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    borderRadius: \"6px\",\n                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    cursor: \"pointer\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    transition: \"all 0.3s ease\",\n                                    backdropFilter: \"blur(10px)\"\n                                },\n                                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                                    size: 12,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                    size: 12,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"16px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: isCollapsed ? \"0 6px\" : \"0 12px\",\n                                transition: \"padding 0.3s ease\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\",\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            onMouseEnter: ()=>setHoveredItem(item.href),\n                                            onMouseLeave: ()=>setHoveredItem(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    padding: isCollapsed ? \"10px 6px\" : \"10px 12px\",\n                                                    borderRadius: \"12px\",\n                                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                    cursor: \"pointer\",\n                                                    position: \"relative\",\n                                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\",\n                                                    background: active ? \"\\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                        \" : hovered ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                          \" : \"transparent\",\n                                                    backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                    border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                    boxShadow: active ? \"\\n                          0 8px 32px rgba(0, 0, 0, 0.1),\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n                        \" : hovered ? \"\\n                            0 4px 16px rgba(0, 0, 0, 0.05),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n                          \" : \"none\",\n                                                    transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                                },\n                                                children: [\n                                                    active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            left: \"-2px\",\n                                                            top: \"50%\",\n                                                            transform: \"translateY(-50%)\",\n                                                            width: \"4px\",\n                                                            height: \"24px\",\n                                                            background: \"\\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\\n                          \",\n                                                            borderRadius: \"0 8px 8px 0\",\n                                                            boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            marginRight: isCollapsed ? \"0\" : \"12px\",\n                                                            borderRadius: \"10px\",\n                                                            background: active ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                          \" : hovered ? \"\\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                            \" : \"transparent\",\n                                                            transition: \"all 0.3s ease\",\n                                                            transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 18,\n                                                            color: colors.sidebar.text,\n                                                            style: {\n                                                                transition: \"all 0.3s ease\",\n                                                                filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"15px\",\n                                                            fontWeight: active ? \"600\" : \"500\",\n                                                            color: colors.sidebar.text,\n                                                            transition: \"all 0.3s ease\",\n                                                            letterSpacing: \"-0.2px\",\n                                                            textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\",\n                                                            opacity: isCollapsed ? 0 : 1,\n                                                            transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                                        },\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    hovered && !active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            right: \"12px\",\n                                                            width: \"6px\",\n                                                            height: \"6px\",\n                                                            borderRadius: \"50%\",\n                                                            background: \"\\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\\n                          \",\n                                                            boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isCollapsed && hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"70px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: \"linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)\",\n                                                color: \"white\",\n                                                padding: \"8px 12px\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                whiteSpace: \"nowrap\",\n                                                zIndex: 1000,\n                                                boxShadow: \"0 8px 24px rgba(0, 0, 0, 0.3)\",\n                                                backdropFilter: \"blur(10px)\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                                pointerEvents: \"none\"\n                                            },\n                                            children: [\n                                                item.label,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-4px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: 0,\n                                                        height: 0,\n                                                        borderTop: \"4px solid transparent\",\n                                                        borderBottom: \"4px solid transparent\",\n                                                        borderRight: \"4px solid rgba(0, 0, 0, 0.9)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.href, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"20px 8px\" : \"20px 16px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\",\n                            background: \"\\n            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\\n          \",\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: isCollapsed ? \"0\" : \"12px\",\n                                padding: isCollapsed ? \"12px 8px\" : \"12px 16px\",\n                                background: \"\\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n            \",\n                                borderRadius: \"16px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                boxShadow: \"\\n              0 8px 32px rgba(0, 0, 0, 0.1),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n            \",\n                                justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        background: \"\\n                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n              \",\n                                        borderRadius: \"12px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        backdropFilter: \"blur(10px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: \"\\n                0 4px 16px rgba(0, 0, 0, 0.1),\\n                inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n              \"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-2px\",\n                                                right: \"-2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"\\n                  radial-gradient(circle, #00E676 0%, #00C853 100%)\\n                \",\n                                                border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                flex: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.sidebar.text,\n                                                        lineHeight: \"1.2\",\n                                                        marginBottom: \"2px\",\n                                                        textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"Alex Chen\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.sidebar.textTertiary,\n                                                        lineHeight: \"1.2\",\n                                                        fontWeight: \"500\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"AI Manager\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"20px\",\n                                                height: \"20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                borderRadius: \"6px\",\n                                                background: \"\\n                    linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                  \",\n                                                transition: \"all 0.3s ease\",\n                                                opacity: isCollapsed ? 0 : 1,\n                                                transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.sidebar.textSecondary,\n                                                    transform: \"rotate(0deg)\",\n                                                    transition: \"transform 0.3s ease\"\n                                                },\n                                                children: \"⌄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: \"\\n            0 32px 80px rgba(0, 0, 0, 0.12),\\n            0 8px 32px rgba(0, 0, 0, 0.08),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\\n            0 0 0 1px rgba(255, 107, 53, 0.1)\\n          \",\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: \"\\n              radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"20 0%, transparent 70%)\\n            \"),\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"rtVtX68IYxImALVkTOCPE5kL79s=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});