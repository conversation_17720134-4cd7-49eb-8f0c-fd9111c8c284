"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"daily-operation\": true,\n        \"manage-staff\": false,\n        \"manage-guests\": true\n    });\n    // Fixoria-inspired color palette\n    const colors = {\n        primary: \"#22C55E\",\n        primaryLight: \"#4ADE80\",\n        primaryDark: \"#16A34A\",\n        accent: \"#F0FDF4\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFAFA\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\",\n            primary: \"#22C55E\" // Primary colored border\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            border: \"#E5E7EB\",\n            hover: \"#F9FAFB\",\n            active: \"#F0FDF4\"\n        }\n    };\n    const navigationSections = [\n        {\n            id: \"daily-operation\",\n            title: \"DAILY OPERATION\",\n            items: [\n                {\n                    href: \"/dashboard\",\n                    label: \"Dashboard\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    isActive: router.pathname === \"/dashboard\"\n                },\n                {\n                    href: \"/reservation\",\n                    label: \"Reservation\",\n                    icon: \"\\uD83D\\uDCC5\",\n                    isActive: router.pathname.startsWith(\"/reservation\"),\n                    hasSubmenu: true\n                },\n                {\n                    href: \"/room-operation\",\n                    label: \"Room Operation\",\n                    icon: \"\\uD83C\\uDFE0\",\n                    isActive: router.pathname === \"/room-operation\"\n                }\n            ]\n        },\n        {\n            id: \"manage-staff\",\n            title: \"MANAGE STAFF\",\n            items: [\n                {\n                    href: \"/manage-staff\",\n                    label: \"Manage Staff\",\n                    icon: \"\\uD83D\\uDC65\",\n                    isActive: router.pathname.startsWith(\"/manage-staff\"),\n                    hasSubmenu: true\n                }\n            ]\n        },\n        {\n            id: \"manage-guests\",\n            title: \"MANAGE GUESTS\",\n            items: [\n                {\n                    href: \"/manage-guests\",\n                    label: \"Manage Guests\",\n                    icon: \"\\uD83D\\uDC64\",\n                    isActive: router.pathname.startsWith(\"/manage-guests\"),\n                    hasSubmenu: true,\n                    subItems: [\n                        {\n                            href: \"/manage-guests/guests-list\",\n                            label: \"Guests List\",\n                            isActive: router.pathname === \"/manage-guests/guests-list\" || router.pathname === \"/meeting\"\n                        },\n                        {\n                            href: \"/manage-guests/reviews\",\n                            label: \"Guests Reviews\",\n                            isActive: router.pathname === \"/manage-guests/reviews\"\n                        }\n                    ]\n                }\n            ]\n        }\n    ];\n    const bottomSections = [\n        {\n            href: \"/promotions\",\n            label: \"Promotions\",\n            icon: \"\\uD83C\\uDFAF\",\n            isActive: router.pathname === \"/promotions\"\n        }\n    ];\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [sectionId]: !prev[sectionId]\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    borderRight: \"1px solid \".concat(colors.sidebar.border),\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light),\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"12px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"32px\",\n                                    height: \"32px\",\n                                    background: colors.primary,\n                                    borderRadius: \"6px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    position: \"relative\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"700\"\n                                    },\n                                    children: \"F\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: colors.text.primary,\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: \"Fixoria ™\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: \"auto\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    background: colors.surfaceElevated,\n                                    borderRadius: \"4px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    cursor: \"pointer\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⚙\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"24px\",\n                                        height: \"24px\",\n                                        background: colors.primary,\n                                        borderRadius: \"50%\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.text.primary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Grand Sylhet Hotel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.text.tertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"3 more hotels\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⌄\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"0\",\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            navigationSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"24px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onClick: ()=>toggleSection(section.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"11px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.tertiary,\n                                                        letterSpacing: \"0.5px\",\n                                                        textTransform: \"uppercase\"\n                                                    },\n                                                    children: section.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        transform: expandedSections[section.id] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.2s ease\"\n                                                    },\n                                                    children: \"⌄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        expandedSections[section.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: \"8px\",\n                                                paddingRight: \"8px\"\n                                            },\n                                            children: section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: item.href,\n                                                            style: {\n                                                                textDecoration: \"none\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    padding: \"8px 12px\",\n                                                                    margin: \"2px 0\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                                    transition: \"all 0.15s ease\"\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                    }\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"16px\",\n                                                                            marginRight: \"12px\",\n                                                                            width: \"20px\",\n                                                                            textAlign: \"center\"\n                                                                        },\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"14px\",\n                                                                            fontWeight: item.isActive ? \"600\" : \"500\",\n                                                                            color: item.isActive ? colors.text.primary : colors.text.secondary,\n                                                                            flex: 1\n                                                                        },\n                                                                        children: item.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    item.hasSubmenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"12px\",\n                                                                            color: colors.text.tertiary,\n                                                                            transform: item.subItems && expandedSections[\"\".concat(section.id, \"-\").concat(item.href)] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                                            transition: \"transform 0.2s ease\"\n                                                                        },\n                                                                        children: \"⌄\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        item.subItems && item.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                paddingLeft: \"32px\",\n                                                                marginTop: \"4px\",\n                                                                marginBottom: \"8px\"\n                                                            },\n                                                            children: item.subItems.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: subItem.href,\n                                                                    style: {\n                                                                        textDecoration: \"none\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            padding: \"6px 12px\",\n                                                                            margin: \"1px 0\",\n                                                                            borderRadius: \"4px\",\n                                                                            cursor: \"pointer\",\n                                                                            backgroundColor: subItem.isActive ? colors.sidebar.active : \"transparent\",\n                                                                            borderLeft: subItem.isActive ? \"2px solid \".concat(colors.primary) : \"2px solid transparent\",\n                                                                            transition: \"all 0.15s ease\"\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                            }\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                            }\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: subItem.isActive ? \"600\" : \"500\",\n                                                                                color: subItem.isActive ? colors.text.primary : colors.text.secondary\n                                                                            },\n                                                                            children: subItem.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, subItem.href, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, item.href, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, section.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"8px\"\n                                },\n                                children: bottomSections.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"8px 12px\",\n                                                margin: \"2px 0\",\n                                                borderRadius: \"6px\",\n                                                cursor: \"pointer\",\n                                                backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                transition: \"all 0.15s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"16px\",\n                                                        marginRight: \"12px\",\n                                                        width: \"20px\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: item.isActive ? \"600\" : \"500\",\n                                                        color: item.isActive ? colors.text.primary : colors.text.secondary\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: \"24px\",\n                            left: \"16px\",\n                            right: \"16px\",\n                            padding: \"16px\",\n                            backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                            borderRadius: \"12px\",\n                            border: \"1px solid rgba(102, 126, 234, 0.1)\",\n                            boxShadow: \"inset 0 1px 2px rgba(102, 126, 234, 0.05)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    color: \"#667eea\",\n                                    marginBottom: \"4px\"\n                                },\n                                children: \"\\uD83D\\uDCA1 Pro Tip\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6c757d\",\n                                    lineHeight: \"1.4\"\n                                },\n                                children: \"Use AI Meeting for personalized mentoring sessions\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8f9fa\",\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"VCfYwMbh02XTMin+trdDom6QSPc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});