"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/tweet-center",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const accountMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (accountMenuRef.current && !accountMenuRef.current.contains(event.target)) {\n                setShowAccountMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: \"\\n        radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"15 0%, transparent 50%),\\n        radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"10 0%, transparent 50%),\\n        linear-gradient(135deg, \").concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\\n      \"),\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"16px\",\n                    boxShadow: \"\\n          0 20px 60px \".concat(colors.sidebar.glow, \",\\n          0 8px 32px rgba(0, 0, 0, 0.15),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n        \"),\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border),\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.sidebar.text,\n                                fontWeight: \"400\",\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                textShadow: \"0 2px 8px rgba(0, 0, 0, 0.3)\",\n                                letterSpacing: \"-1px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"6px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.2s ease\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? \"linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\" : hovered ? \"linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\" : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)\" : \"none\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-1px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: \"linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)\",\n                                                        borderRadius: \"0 6px 6px 0\",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"6px\",\n                                                        background: active ? \"linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\" : \"transparent\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            filter: active ? \"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        letterSpacing: \"-0.3px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: accountMenuRef,\n                        style: {\n                            padding: \"16px 12px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\",\n                            background: \"radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\",\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setShowAccountMenu(!showAccountMenu),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 12px\",\n                                    background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\",\n                                    borderRadius: \"8px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"24px\",\n                                            height: \"24px\",\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\",\n                                            borderRadius: \"6px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                            flexShrink: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                                },\n                                                children: \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"6px\",\n                                                    height: \"6px\",\n                                                    borderRadius: \"50%\",\n                                                    background: \"radial-gradient(circle, #00E676 0%, #00C853 100%)\",\n                                                    border: \"1px solid rgba(255, 255, 255, 0.9)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            minWidth: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.sidebar.text,\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"1px\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                    overflow: \"hidden\",\n                                                    textOverflow: \"ellipsis\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: \"Alex Chen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"10px\",\n                                                    color: colors.sidebar.textTertiary,\n                                                    lineHeight: \"1.2\",\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: \"AI Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"12px\",\n                                            height: \"12px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            borderRadius: \"3px\",\n                                            background: \"linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\",\n                                            flexShrink: 0\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"8px\",\n                                                color: colors.sidebar.textSecondary,\n                                                transform: showAccountMenu ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                transition: \"transform 0.2s ease\"\n                                            },\n                                            children: \"⌄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            showAccountMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    bottom: \"100%\",\n                                    left: \"12px\",\n                                    right: \"12px\",\n                                    marginBottom: \"8px\",\n                                    background: \"white\",\n                                    borderRadius: \"12px\",\n                                    boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.15)\",\n                                    border: \"1px solid rgba(0, 0, 0, 0.1)\",\n                                    overflow: \"hidden\",\n                                    zIndex: 1000\n                                },\n                                children: [\n                                    {\n                                        label: \"Account Settings\",\n                                        icon: \"⚙️\"\n                                    },\n                                    {\n                                        label: \"Subscription\",\n                                        icon: \"\\uD83D\\uDCB3\"\n                                    },\n                                    {\n                                        label: \"Billing\",\n                                        icon: \"\\uD83D\\uDCC4\"\n                                    },\n                                    {\n                                        label: \"Help & Support\",\n                                        icon: \"❓\"\n                                    },\n                                    {\n                                        label: \"Sign Out\",\n                                        icon: \"\\uD83D\\uDEAA\"\n                                    }\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"12px 16px\",\n                                            cursor: \"pointer\",\n                                            transition: \"background 0.2s ease\",\n                                            borderBottom: index < 4 ? \"1px solid #F3F4F6\" : \"none\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"12px\"\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.background = \"#F9FAFB\",\n                                        onMouseLeave: (e)=>e.currentTarget.style.background = \"transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\"\n                                                },\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\"\n                                                },\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: \"\\n            0 32px 80px rgba(0, 0, 0, 0.12),\\n            0 8px 32px rgba(0, 0, 0, 0.08),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\\n            0 0 0 1px rgba(255, 107, 53, 0.1)\\n          \",\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: \"\\n              radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"20 0%, transparent 70%)\\n            \"),\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"Y9ibl4BvxWP/PISq+ylbxpdDF5Q=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});