"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MeetingPage = ()=>{\n    _s();\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isCreatingRoom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            if (isCreatingRoom.current) return; // Prevent multiple calls\n            isCreatingRoom.current = true;\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setLoading(false);\n            } finally{\n                isCreatingRoom.current = false;\n            }\n        };\n        // Only create room if we don't have one yet and not already creating\n        if (!roomUrl && loading && !isCreatingRoom.current) {\n            createRoom();\n        }\n    }, []); // Remove dependencies to run only once\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MeetingPage, \"0l/pyavyvD3f2bGRK8lnPBPa/PI=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9tZWV0aW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUyRDtBQUNIO0FBSXhELE1BQU1LLGNBQWtDOztJQUN0QyxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR0wsK0NBQVFBLENBQWdCO0lBQ3RELE1BQU0sQ0FBQ00sU0FBU0MsV0FBVyxHQUFHUCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNUSxpQkFBaUJQLDZDQUFNQSxDQUFDO0lBRTlCLHFDQUFxQztJQUNyQ0YsZ0RBQVNBLENBQUM7UUFDUixNQUFNVSxhQUFhO1lBQ2pCLElBQUlELGVBQWVFLE9BQU8sRUFBRSxRQUFRLHlCQUF5QjtZQUM3REYsZUFBZUUsT0FBTyxHQUFHO1lBRXpCLElBQUk7Z0JBQ0YsbURBQW1EO2dCQUNuRCxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sbUJBQW1CO29CQUM5Q0MsUUFBUTtvQkFDUkMsU0FBUzt3QkFBRSxnQkFBZ0I7b0JBQW1CO29CQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUFFQyxVQUFVLGtCQUFrQkMsS0FBS0MsR0FBRztvQkFBRztnQkFDaEU7Z0JBRUEsSUFBSSxDQUFDVCxTQUFTVSxFQUFFLEVBQUU7b0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTUMsT0FBTyxNQUFNWixTQUFTYSxJQUFJO2dCQUNoQ25CLFdBQVdrQixLQUFLRSxHQUFHO2dCQUNuQmxCLFdBQVc7WUFDYixFQUFFLE9BQU9tQixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtnQkFDdEMsMENBQTBDO2dCQUMxQyxNQUFNRSxjQUFjO2dCQUNwQnZCLFdBQVd1QjtnQkFDWHJCLFdBQVc7WUFDYixTQUFVO2dCQUNSQyxlQUFlRSxPQUFPLEdBQUc7WUFDM0I7UUFDRjtRQUVBLHFFQUFxRTtRQUNyRSxJQUFJLENBQUNOLFdBQVdFLFdBQVcsQ0FBQ0UsZUFBZUUsT0FBTyxFQUFFO1lBQ2xERDtRQUNGO0lBQ0YsR0FBRyxFQUFFLEdBQUcsdUNBQXVDO0lBRS9DLHVCQUF1QjtJQUN2QixJQUFJSCxTQUFTO1FBQ1gscUJBQ0UsOERBQUN1QjtZQUFJQyxPQUFPO2dCQUNWQyxRQUFRO2dCQUNSQyxTQUFTO2dCQUNUQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaQyxpQkFBaUI7WUFDbkI7c0JBQ0UsNEVBQUNOO2dCQUFJQyxPQUFPO29CQUFFTSxXQUFXO2dCQUFTOztrQ0FDaEMsOERBQUNDO3dCQUFHUCxPQUFPOzRCQUFFUSxPQUFPOzRCQUF1QkMsY0FBYzt3QkFBTztrQ0FBRzs7Ozs7O2tDQUNuRSw4REFBQ1Y7d0JBQUlDLE9BQU87NEJBQUVRLE9BQU87d0JBQU87a0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXZDO0lBTUEsNkNBQTZDO0lBQzdDLElBQUksQ0FBQ2xDLFNBQVM7UUFDWixxQkFDRSw4REFBQ3lCO1lBQUlDLE9BQU87Z0JBQ1ZDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLGlCQUFpQjtZQUNuQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLFdBQVc7Z0JBQVM7O2tDQUNoQyw4REFBQ0M7d0JBQUdQLE9BQU87NEJBQUVRLE9BQU87NEJBQXVCQyxjQUFjO3dCQUFPO2tDQUFHOzs7Ozs7a0NBQ25FLDhEQUFDVjt3QkFBSUMsT0FBTzs0QkFBRVEsT0FBTzt3QkFBTztrQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkM7SUFFQSxxQkFDRSw4REFBQ1Q7UUFBSUMsT0FBTztZQUFFQyxRQUFRO1lBQVNDLFNBQVM7WUFBUVEsZUFBZTtZQUFVTCxpQkFBaUI7WUFBV00sVUFBVTtRQUFXOzswQkFFeEgsOERBQUNaO2dCQUFJQyxPQUFPO29CQUNWWSxTQUFTO29CQUNUUCxpQkFBaUI7b0JBQ2pCUSxjQUFjO29CQUNkWCxTQUFTO29CQUNUQyxnQkFBZ0I7b0JBQ2hCQyxZQUFZO2dCQUNkOztrQ0FDRSw4REFBQ1U7d0JBQUdkLE9BQU87NEJBQUVRLE9BQU87NEJBQXVCTyxRQUFROzRCQUFHQyxVQUFVO3dCQUFPO2tDQUFHOzs7Ozs7a0NBQzFFLDhEQUFDakI7d0JBQUlDLE9BQU87NEJBQUVnQixVQUFVOzRCQUFRUixPQUFPO3dCQUFPO2tDQUFHOzs7Ozs7Ozs7Ozs7MEJBTW5ELDhEQUFDUztnQkFDQ0MsS0FBSzVDO2dCQUNMNkMsT0FBTTtnQkFDTm5CLE9BQU87b0JBQ0xvQixPQUFPO29CQUNQbkIsUUFBUTtvQkFDUm9CLFFBQVE7b0JBQ1JDLGNBQWM7Z0JBQ2hCO2dCQUNBQyxPQUFNOzs7Ozs7MEJBSVIsOERBQUNDO2dCQUNDQyxTQUFTLElBQU1DLE1BQU07Z0JBQ3JCMUIsT0FBTztvQkFDTFcsVUFBVTtvQkFDVmdCLFFBQVE7b0JBQ1JDLE9BQU87b0JBQ1B2QixpQkFBaUI7b0JBQ2pCRyxPQUFPO29CQUNQYSxRQUFRO29CQUNSQyxjQUFjO29CQUNkRixPQUFPO29CQUNQbkIsUUFBUTtvQkFDUmUsVUFBVTtvQkFDVmEsUUFBUTtvQkFDUkMsV0FBVztvQkFDWEMsWUFBWTtvQkFDWkMsUUFBUTtnQkFDVjtnQkFDQUMsYUFBYSxDQUFDQztvQkFDWkEsRUFBRUMsYUFBYSxDQUFDbkMsS0FBSyxDQUFDb0MsU0FBUyxHQUFHO29CQUNsQ0YsRUFBRUMsYUFBYSxDQUFDbkMsS0FBSyxDQUFDOEIsU0FBUyxHQUFHO2dCQUNwQztnQkFDQU8sWUFBWSxDQUFDSDtvQkFDWEEsRUFBRUMsYUFBYSxDQUFDbkMsS0FBSyxDQUFDb0MsU0FBUyxHQUFHO29CQUNsQ0YsRUFBRUMsYUFBYSxDQUFDbkMsS0FBSyxDQUFDOEIsU0FBUyxHQUFHO2dCQUNwQztnQkFDQVAsT0FBTTswQkFDUDs7Ozs7Ozs7Ozs7O0FBS1A7R0FsSk1sRDtLQUFBQTtBQW9KTkEsWUFBWWlFLFNBQVMsR0FBRyxTQUFTQSxVQUFVQyxJQUFrQjtJQUMzRCxxQkFDRSw4REFBQ25FLGlFQUFhQTtrQkFDWG1FOzs7Ozs7QUFHUDtBQUVBLCtEQUFlbEUsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9tZWV0aW5nLnRzeD85NGUzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBTaWRlYmFyTGF5b3V0IGZyb20gJy4uL2NvbXBvbmVudHMvU2lkZWJhckxheW91dCc7XG5pbXBvcnQgdHlwZSB7IFJlYWN0RWxlbWVudCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2VXaXRoTGF5b3V0IH0gZnJvbSAnLi9fYXBwJztcblxuY29uc3QgTWVldGluZ1BhZ2U6IE5leHRQYWdlV2l0aExheW91dCA9ICgpID0+IHtcbiAgY29uc3QgW3Jvb21VcmwsIHNldFJvb21VcmxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBpc0NyZWF0aW5nUm9vbSA9IHVzZVJlZihmYWxzZSk7XG5cbiAgLy8gQ3JlYXRlIERhaWx5LmNvIHJvb20gdXNpbmcgdGhlIEFQSVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNyZWF0ZVJvb20gPSBhc3luYyAoKSA9PiB7XG4gICAgICBpZiAoaXNDcmVhdGluZ1Jvb20uY3VycmVudCkgcmV0dXJuOyAvLyBQcmV2ZW50IG11bHRpcGxlIGNhbGxzXG4gICAgICBpc0NyZWF0aW5nUm9vbS5jdXJyZW50ID0gdHJ1ZTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gQ3JlYXRlIGEgcm9vbSBzZXJ2ZXItc2lkZSB1c2luZyB0aGUgRGFpbHkuY28gQVBJXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZGFpbHktcm9vbScsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHJvb21OYW1lOiAnZXhpZS1tZWV0aW5nLScgKyBEYXRlLm5vdygpIH0pXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjcmVhdGUgcm9vbScpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0Um9vbVVybChkYXRhLnVybCk7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgcm9vbTonLCBlcnJvcik7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIGEgZGVtbyByb29tIFVSTCBmb3IgdGVzdGluZ1xuICAgICAgICBjb25zdCBmYWxsYmFja1VybCA9ICdodHRwczovL2V4aWUtYWkuZGFpbHkuY28vZXhpZS1kZW1vLXJvb20nO1xuICAgICAgICBzZXRSb29tVXJsKGZhbGxiYWNrVXJsKTtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBpc0NyZWF0aW5nUm9vbS5jdXJyZW50ID0gZmFsc2U7XG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vIE9ubHkgY3JlYXRlIHJvb20gaWYgd2UgZG9uJ3QgaGF2ZSBvbmUgeWV0IGFuZCBub3QgYWxyZWFkeSBjcmVhdGluZ1xuICAgIGlmICghcm9vbVVybCAmJiBsb2FkaW5nICYmICFpc0NyZWF0aW5nUm9vbS5jdXJyZW50KSB7XG4gICAgICBjcmVhdGVSb29tKCk7XG4gICAgfVxuICB9LCBbXSk7IC8vIFJlbW92ZSBkZXBlbmRlbmNpZXMgdG8gcnVuIG9ubHkgb25jZVxuXG4gIC8vIFJlbmRlciBsb2FkaW5nIHN0YXRlXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgaGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmOGY4ZjgnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJ3ZhcigtLWFjY2VudC1jb2xvciknLCBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5TZXR0aW5nIHVwIHlvdXIgbWVldGluZy4uLjwvaDI+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJyM2NjYnIH19PlBsZWFzZSB3YWl0IHdoaWxlIHdlIHByZXBhcmUgeW91ciByb29tPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG5cblxuXG5cbiAgLy8gUmVuZGVyIHRoZSBVSSBvbmx5IGlmIHJvb21VcmwgaXMgYXZhaWxhYmxlXG4gIGlmICghcm9vbVVybCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4J1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICd2YXIoLS1hY2NlbnQtY29sb3IpJywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+U2V0dGluZyB1cCB5b3VyIG1lZXRpbmcuLi48L2gyPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjNjY2JyB9fT5QbGVhc2Ugd2FpdCB3aGlsZSB3ZSBwcmVwYXJlIHlvdXIgcm9vbTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgaGVpZ2h0OiAnMTAwdmgnLCBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4JywgcG9zaXRpb246ICdyZWxhdGl2ZScgfX0+XG4gICAgICB7LyogSGVhZGVyIHdpdGggbWVldGluZyBpbmZvICovfVxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBwYWRkaW5nOiAnMTZweCcsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICNlMGUwZTAnLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICB9fT5cbiAgICAgICAgPGgxIHN0eWxlPXt7IGNvbG9yOiAndmFyKC0tYWNjZW50LWNvbG9yKScsIG1hcmdpbjogMCwgZm9udFNpemU6ICcyMHB4JyB9fT5FeGllIEFJIE1lZXRpbmc8L2gxPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgQUktcG93ZXJlZCBtZW50b3Jpbmcgc2Vzc2lvbiB3aXRoIERhaWx5LmNvXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEYWlseS5jbyBpZnJhbWUgLSB0aGlzIGdpdmVzIHVzIHRoZSBmdWxsIEdvb2dsZSBNZWV0LXN0eWxlIFVJICovfVxuICAgICAgPGlmcmFtZVxuICAgICAgICBzcmM9e3Jvb21Vcmx9XG4gICAgICAgIGFsbG93PVwiY2FtZXJhOyBtaWNyb3Bob25lOyBmdWxsc2NyZWVuOyBkaXNwbGF5LWNhcHR1cmU7IGF1dG9wbGF5XCJcbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgIGhlaWdodDogJzEwMCUnLFxuICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzAgMCA4cHggOHB4J1xuICAgICAgICB9fVxuICAgICAgICB0aXRsZT1cIkRhaWx5LmNvIFZpZGVvIE1lZXRpbmdcIlxuICAgICAgLz5cblxuICAgICAgey8qIEZsb2F0aW5nIEFJIEFzc2lzdGFudCBCdXR0b24gKi99XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IGFsZXJ0KCdBSSBBc3Npc3RhbnQgaW50ZWdyYXRpb24gY29taW5nIHNvb24hIFRoaXMgd2lsbCBhbGxvdyB5b3UgdG8gdGFsayB0byBFeGllIGR1cmluZyB5b3VyIERhaWx5LmNvIG1lZXRpbmcuJyl9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgYm90dG9tOiAnMjRweCcsXG4gICAgICAgICAgcmlnaHQ6ICcyNHB4JyxcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1hY2NlbnQtY29sb3IpJyxcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgIHdpZHRoOiAnNjBweCcsXG4gICAgICAgICAgaGVpZ2h0OiAnNjBweCcsXG4gICAgICAgICAgZm9udFNpemU6ICcyNHB4JyxcbiAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyknLFxuICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgfX1cbiAgICAgICAgb25Nb3VzZU92ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxLjEpJztcbiAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYm94U2hhZG93ID0gJzAgNnB4IDE2cHggcmdiYSgwLCAxMjMsIDI1NSwgMC40KSc7XG4gICAgICAgIH19XG4gICAgICAgIG9uTW91c2VPdXQ9eyhlKSA9PiB7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxKSc7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9ICcwIDRweCAxMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyknO1xuICAgICAgICB9fVxuICAgICAgICB0aXRsZT1cIlRhbGsgdG8gRXhpZSBBSVwiXG4gICAgICA+XG4gICAgICAgIPCfpJZcbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuTWVldGluZ1BhZ2UuZ2V0TGF5b3V0ID0gZnVuY3Rpb24gZ2V0TGF5b3V0KHBhZ2U6IFJlYWN0RWxlbWVudCkge1xuICByZXR1cm4gKFxuICAgIDxTaWRlYmFyTGF5b3V0PlxuICAgICAge3BhZ2V9XG4gICAgPC9TaWRlYmFyTGF5b3V0PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTWVldGluZ1BhZ2U7Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJTaWRlYmFyTGF5b3V0IiwiTWVldGluZ1BhZ2UiLCJyb29tVXJsIiwic2V0Um9vbVVybCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiaXNDcmVhdGluZ1Jvb20iLCJjcmVhdGVSb29tIiwiY3VycmVudCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyb29tTmFtZSIsIkRhdGUiLCJub3ciLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJ1cmwiLCJlcnJvciIsImNvbnNvbGUiLCJmYWxsYmFja1VybCIsImRpdiIsInN0eWxlIiwiaGVpZ2h0IiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImJhY2tncm91bmRDb2xvciIsInRleHRBbGlnbiIsImgyIiwiY29sb3IiLCJtYXJnaW5Cb3R0b20iLCJmbGV4RGlyZWN0aW9uIiwicG9zaXRpb24iLCJwYWRkaW5nIiwiYm9yZGVyQm90dG9tIiwiaDEiLCJtYXJnaW4iLCJmb250U2l6ZSIsImlmcmFtZSIsInNyYyIsImFsbG93Iiwid2lkdGgiLCJib3JkZXIiLCJib3JkZXJSYWRpdXMiLCJ0aXRsZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhbGVydCIsImJvdHRvbSIsInJpZ2h0IiwiY3Vyc29yIiwiYm94U2hhZG93IiwidHJhbnNpdGlvbiIsInpJbmRleCIsIm9uTW91c2VPdmVyIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJ0cmFuc2Zvcm0iLCJvbk1vdXNlT3V0IiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});