"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MeetingPage = ()=>{\n    // Use a reliable public Daily.co room that works without API key\n    // This is Daily.co's official demo room that's always available\n    const roomUrl = \"https://demo.daily.co/hello\";\n    const loading = false;\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show loading state while creating room\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show error if no room URL\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#dc3545\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Unable to create meeting room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Please try refreshing the page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        style: {\n                            backgroundColor: \"var(--accent-color)\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            padding: \"12px 24px\",\n                            fontSize: \"16px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Refresh Page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9tZWV0aW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFFMEI7QUFDOEI7QUFJeEQsTUFBTUUsY0FBa0M7SUFDdEMsaUVBQWlFO0lBQ2pFLGdFQUFnRTtJQUNoRSxNQUFNQyxVQUFVO0lBQ2hCLE1BQU1DLFVBQVU7SUFFaEIsdUJBQXVCO0lBQ3ZCLElBQUlBLFNBQVM7UUFDWCxxQkFDRSw4REFBQ0M7WUFBSUMsT0FBTztnQkFDVkMsUUFBUTtnQkFDUkMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsaUJBQWlCO1lBQ25CO3NCQUNFLDRFQUFDTjtnQkFBSUMsT0FBTztvQkFBRU0sV0FBVztnQkFBUzs7a0NBQ2hDLDhEQUFDQzt3QkFBR1AsT0FBTzs0QkFBRVEsT0FBTzs0QkFBdUJDLGNBQWM7d0JBQU87a0NBQUc7Ozs7OztrQ0FDbkUsOERBQUNWO3dCQUFJQyxPQUFPOzRCQUFFUSxPQUFPO3dCQUFPO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztJQUl2QztJQU1BLDZDQUE2QztJQUM3QyxJQUFJLENBQUNYLFNBQVM7UUFDWixxQkFDRSw4REFBQ0U7WUFBSUMsT0FBTztnQkFDVkMsUUFBUTtnQkFDUkMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsaUJBQWlCO1lBQ25CO3NCQUNFLDRFQUFDTjtnQkFBSUMsT0FBTztvQkFBRU0sV0FBVztnQkFBUzs7a0NBQ2hDLDhEQUFDQzt3QkFBR1AsT0FBTzs0QkFBRVEsT0FBTzs0QkFBdUJDLGNBQWM7d0JBQU87a0NBQUc7Ozs7OztrQ0FDbkUsOERBQUNWO3dCQUFJQyxPQUFPOzRCQUFFUSxPQUFPO3dCQUFPO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztJQUl2QztJQUVBLHlDQUF5QztJQUN6QyxJQUFJVixTQUFTO1FBQ1gscUJBQ0UsOERBQUNDO1lBQUlDLE9BQU87Z0JBQ1ZDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLGlCQUFpQjtZQUNuQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLFdBQVc7Z0JBQVM7O2tDQUNoQyw4REFBQ0M7d0JBQUdQLE9BQU87NEJBQUVRLE9BQU87NEJBQXVCQyxjQUFjO3dCQUFPO2tDQUFHOzs7Ozs7a0NBQ25FLDhEQUFDVjt3QkFBSUMsT0FBTzs0QkFBRVEsT0FBTzt3QkFBTztrQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkM7SUFFQSw0QkFBNEI7SUFDNUIsSUFBSSxDQUFDWCxTQUFTO1FBQ1oscUJBQ0UsOERBQUNFO1lBQUlDLE9BQU87Z0JBQ1ZDLFFBQVE7Z0JBQ1JDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLGlCQUFpQjtZQUNuQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLFdBQVc7Z0JBQVM7O2tDQUNoQyw4REFBQ0M7d0JBQUdQLE9BQU87NEJBQUVRLE9BQU87NEJBQVdDLGNBQWM7d0JBQU87a0NBQUc7Ozs7OztrQ0FDdkQsOERBQUNWO3dCQUFJQyxPQUFPOzRCQUFFUSxPQUFPOzRCQUFRQyxjQUFjO3dCQUFPO2tDQUFHOzs7Ozs7a0NBQ3JELDhEQUFDQzt3QkFDQ0MsU0FBUyxJQUFNQyxPQUFPQyxRQUFRLENBQUNDLE1BQU07d0JBQ3JDZCxPQUFPOzRCQUNMSyxpQkFBaUI7NEJBQ2pCRyxPQUFPOzRCQUNQTyxRQUFROzRCQUNSQyxjQUFjOzRCQUNkQyxTQUFTOzRCQUNUQyxVQUFVOzRCQUNWQyxRQUFRO3dCQUNWO2tDQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztJQU1UO0lBRUEscUJBQ0UsOERBQUNwQjtRQUFJQyxPQUFPO1lBQUVDLFFBQVE7WUFBU0MsU0FBUztZQUFRa0IsZUFBZTtZQUFVZixpQkFBaUI7WUFBV2dCLFVBQVU7UUFBVzs7MEJBRXhILDhEQUFDdEI7Z0JBQUlDLE9BQU87b0JBQ1ZpQixTQUFTO29CQUNUWixpQkFBaUI7b0JBQ2pCaUIsY0FBYztvQkFDZHBCLFNBQVM7b0JBQ1RDLGdCQUFnQjtvQkFDaEJDLFlBQVk7Z0JBQ2Q7O2tDQUNFLDhEQUFDbUI7d0JBQUd2QixPQUFPOzRCQUFFUSxPQUFPOzRCQUF1QmdCLFFBQVE7NEJBQUdOLFVBQVU7d0JBQU87a0NBQUc7Ozs7OztrQ0FDMUUsOERBQUNuQjt3QkFBSUMsT0FBTzs0QkFBRWtCLFVBQVU7NEJBQVFWLE9BQU87d0JBQU87a0NBQUc7Ozs7Ozs7Ozs7OzswQkFNbkQsOERBQUNpQjtnQkFDQ0MsS0FBSzdCO2dCQUNMOEIsT0FBTTtnQkFDTjNCLE9BQU87b0JBQ0w0QixPQUFPO29CQUNQM0IsUUFBUTtvQkFDUmMsUUFBUTtvQkFDUkMsY0FBYztnQkFDaEI7Z0JBQ0FhLE9BQU07Ozs7OzswQkFJUiw4REFBQ25CO2dCQUNDQyxTQUFTLElBQU1tQixNQUFNO2dCQUNyQjlCLE9BQU87b0JBQ0xxQixVQUFVO29CQUNWVSxRQUFRO29CQUNSQyxPQUFPO29CQUNQM0IsaUJBQWlCO29CQUNqQkcsT0FBTztvQkFDUE8sUUFBUTtvQkFDUkMsY0FBYztvQkFDZFksT0FBTztvQkFDUDNCLFFBQVE7b0JBQ1JpQixVQUFVO29CQUNWQyxRQUFRO29CQUNSYyxXQUFXO29CQUNYQyxZQUFZO29CQUNaQyxRQUFRO2dCQUNWO2dCQUNBQyxhQUFhLENBQUNDO29CQUNaQSxFQUFFQyxhQUFhLENBQUN0QyxLQUFLLENBQUN1QyxTQUFTLEdBQUc7b0JBQ2xDRixFQUFFQyxhQUFhLENBQUN0QyxLQUFLLENBQUNpQyxTQUFTLEdBQUc7Z0JBQ3BDO2dCQUNBTyxZQUFZLENBQUNIO29CQUNYQSxFQUFFQyxhQUFhLENBQUN0QyxLQUFLLENBQUN1QyxTQUFTLEdBQUc7b0JBQ2xDRixFQUFFQyxhQUFhLENBQUN0QyxLQUFLLENBQUNpQyxTQUFTLEdBQUc7Z0JBQ3BDO2dCQUNBSixPQUFNOzBCQUNQOzs7Ozs7Ozs7Ozs7QUFLUDtLQS9KTWpDO0FBaUtOQSxZQUFZNkMsU0FBUyxHQUFHLFNBQVNBLFVBQVVDLElBQWtCO0lBQzNELHFCQUNFLDhEQUFDL0MsaUVBQWFBO2tCQUNYK0M7Ozs7OztBQUdQO0FBRUEsK0RBQWU5QyxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL21lZXRpbmcudHN4Pzk0ZTMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFNpZGViYXJMYXlvdXQgZnJvbSAnLi4vY29tcG9uZW50cy9TaWRlYmFyTGF5b3V0JztcbmltcG9ydCB0eXBlIHsgUmVhY3RFbGVtZW50IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBOZXh0UGFnZVdpdGhMYXlvdXQgfSBmcm9tICcuL19hcHAnO1xuXG5jb25zdCBNZWV0aW5nUGFnZTogTmV4dFBhZ2VXaXRoTGF5b3V0ID0gKCkgPT4ge1xuICAvLyBVc2UgYSByZWxpYWJsZSBwdWJsaWMgRGFpbHkuY28gcm9vbSB0aGF0IHdvcmtzIHdpdGhvdXQgQVBJIGtleVxuICAvLyBUaGlzIGlzIERhaWx5LmNvJ3Mgb2ZmaWNpYWwgZGVtbyByb29tIHRoYXQncyBhbHdheXMgYXZhaWxhYmxlXG4gIGNvbnN0IHJvb21VcmwgPSAnaHR0cHM6Ly9kZW1vLmRhaWx5LmNvL2hlbGxvJztcbiAgY29uc3QgbG9hZGluZyA9IGZhbHNlO1xuXG4gIC8vIFJlbmRlciBsb2FkaW5nIHN0YXRlXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgaGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmOGY4ZjgnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJ3ZhcigtLWFjY2VudC1jb2xvciknLCBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5TZXR0aW5nIHVwIHlvdXIgbWVldGluZy4uLjwvaDI+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJyM2NjYnIH19PlBsZWFzZSB3YWl0IHdoaWxlIHdlIHByZXBhcmUgeW91ciByb29tPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG5cblxuXG5cbiAgLy8gUmVuZGVyIHRoZSBVSSBvbmx5IGlmIHJvb21VcmwgaXMgYXZhaWxhYmxlXG4gIGlmICghcm9vbVVybCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4J1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICd2YXIoLS1hY2NlbnQtY29sb3IpJywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+U2V0dGluZyB1cCB5b3VyIG1lZXRpbmcuLi48L2gyPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjNjY2JyB9fT5QbGVhc2Ugd2FpdCB3aGlsZSB3ZSBwcmVwYXJlIHlvdXIgcm9vbTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBTaG93IGxvYWRpbmcgc3RhdGUgd2hpbGUgY3JlYXRpbmcgcm9vbVxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4J1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICd2YXIoLS1hY2NlbnQtY29sb3IpJywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+U2V0dGluZyB1cCB5b3VyIG1lZXRpbmcuLi48L2gyPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjNjY2JyB9fT5QbGVhc2Ugd2FpdCB3aGlsZSB3ZSBwcmVwYXJlIHlvdXIgcm9vbTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBTaG93IGVycm9yIGlmIG5vIHJvb20gVVJMXG4gIGlmICghcm9vbVVybCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4J1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICcjZGMzNTQ1JywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+VW5hYmxlIHRvIGNyZWF0ZSBtZWV0aW5nIHJvb208L2gyPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjNjY2JywgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+UGxlYXNlIHRyeSByZWZyZXNoaW5nIHRoZSBwYWdlPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndmFyKC0tYWNjZW50LWNvbG9yKScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMjRweCcsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIFJlZnJlc2ggUGFnZVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgaGVpZ2h0OiAnMTAwdmgnLCBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOGY4JywgcG9zaXRpb246ICdyZWxhdGl2ZScgfX0+XG4gICAgICB7LyogSGVhZGVyIHdpdGggbWVldGluZyBpbmZvICovfVxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBwYWRkaW5nOiAnMTZweCcsXG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICNlMGUwZTAnLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICB9fT5cbiAgICAgICAgPGgxIHN0eWxlPXt7IGNvbG9yOiAndmFyKC0tYWNjZW50LWNvbG9yKScsIG1hcmdpbjogMCwgZm9udFNpemU6ICcyMHB4JyB9fT5FeGllIEFJIE1lZXRpbmc8L2gxPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgQUktcG93ZXJlZCBtZW50b3Jpbmcgc2Vzc2lvbiB3aXRoIERhaWx5LmNvXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEYWlseS5jbyBpZnJhbWUgLSB0aGlzIGdpdmVzIHVzIHRoZSBmdWxsIEdvb2dsZSBNZWV0LXN0eWxlIFVJICovfVxuICAgICAgPGlmcmFtZVxuICAgICAgICBzcmM9e3Jvb21Vcmx9XG4gICAgICAgIGFsbG93PVwiY2FtZXJhOyBtaWNyb3Bob25lOyBmdWxsc2NyZWVuOyBkaXNwbGF5LWNhcHR1cmU7IGF1dG9wbGF5XCJcbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgIGhlaWdodDogJzEwMCUnLFxuICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzAgMCA4cHggOHB4J1xuICAgICAgICB9fVxuICAgICAgICB0aXRsZT1cIkRhaWx5LmNvIFZpZGVvIE1lZXRpbmdcIlxuICAgICAgLz5cblxuICAgICAgey8qIEZsb2F0aW5nIEFJIEFzc2lzdGFudCBCdXR0b24gKi99XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IGFsZXJ0KCdBSSBBc3Npc3RhbnQgaW50ZWdyYXRpb24gY29taW5nIHNvb24hIFRoaXMgd2lsbCBhbGxvdyB5b3UgdG8gdGFsayB0byBFeGllIGR1cmluZyB5b3VyIERhaWx5LmNvIG1lZXRpbmcuJyl9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgYm90dG9tOiAnMjRweCcsXG4gICAgICAgICAgcmlnaHQ6ICcyNHB4JyxcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd2YXIoLS1hY2NlbnQtY29sb3IpJyxcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgIHdpZHRoOiAnNjBweCcsXG4gICAgICAgICAgaGVpZ2h0OiAnNjBweCcsXG4gICAgICAgICAgZm9udFNpemU6ICcyNHB4JyxcbiAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyknLFxuICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgfX1cbiAgICAgICAgb25Nb3VzZU92ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxLjEpJztcbiAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYm94U2hhZG93ID0gJzAgNnB4IDE2cHggcmdiYSgwLCAxMjMsIDI1NSwgMC40KSc7XG4gICAgICAgIH19XG4gICAgICAgIG9uTW91c2VPdXQ9eyhlKSA9PiB7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxKSc7XG4gICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9ICcwIDRweCAxMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMyknO1xuICAgICAgICB9fVxuICAgICAgICB0aXRsZT1cIlRhbGsgdG8gRXhpZSBBSVwiXG4gICAgICA+XG4gICAgICAgIPCfpJZcbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuTWVldGluZ1BhZ2UuZ2V0TGF5b3V0ID0gZnVuY3Rpb24gZ2V0TGF5b3V0KHBhZ2U6IFJlYWN0RWxlbWVudCkge1xuICByZXR1cm4gKFxuICAgIDxTaWRlYmFyTGF5b3V0PlxuICAgICAge3BhZ2V9XG4gICAgPC9TaWRlYmFyTGF5b3V0PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTWVldGluZ1BhZ2U7Il0sIm5hbWVzIjpbIlJlYWN0IiwiU2lkZWJhckxheW91dCIsIk1lZXRpbmdQYWdlIiwicm9vbVVybCIsImxvYWRpbmciLCJkaXYiLCJzdHlsZSIsImhlaWdodCIsImRpc3BsYXkiLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJiYWNrZ3JvdW5kQ29sb3IiLCJ0ZXh0QWxpZ24iLCJoMiIsImNvbG9yIiwibWFyZ2luQm90dG9tIiwiYnV0dG9uIiwib25DbGljayIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwiYm9yZGVyIiwiYm9yZGVyUmFkaXVzIiwicGFkZGluZyIsImZvbnRTaXplIiwiY3Vyc29yIiwiZmxleERpcmVjdGlvbiIsInBvc2l0aW9uIiwiYm9yZGVyQm90dG9tIiwiaDEiLCJtYXJnaW4iLCJpZnJhbWUiLCJzcmMiLCJhbGxvdyIsIndpZHRoIiwidGl0bGUiLCJhbGVydCIsImJvdHRvbSIsInJpZ2h0IiwiYm94U2hhZG93IiwidHJhbnNpdGlvbiIsInpJbmRleCIsIm9uTW91c2VPdmVyIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJ0cmFuc2Zvcm0iLCJvbk1vdXNlT3V0IiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});