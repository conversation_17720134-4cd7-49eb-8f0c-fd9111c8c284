"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MeetingPage = ()=>{\n    _s();\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setLoading(false);\n            }\n        };\n        // Only create room if we don't have one yet\n        if (!roomUrl && loading) {\n            createRoom();\n        }\n    }, [\n        roomUrl,\n        loading\n    ]); // Add dependencies to prevent infinite loop\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MeetingPage, \"3bBrBGo36FRf5a6kRNxNOBdE9KA=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});