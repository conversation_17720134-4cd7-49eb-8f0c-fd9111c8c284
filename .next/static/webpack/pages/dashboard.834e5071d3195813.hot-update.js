"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: \"\\n        radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"15 0%, transparent 50%),\\n        radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"10 0%, transparent 50%),\\n        linear-gradient(135deg, \").concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\\n      \"),\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"20px\",\n                    boxShadow: \"\\n          0 20px 60px \".concat(colors.sidebar.glow, \",\\n          0 8px 32px rgba(0, 0, 0, 0.15),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n        \"),\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border),\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            position: \"relative\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"48px\",\n                                color: colors.sidebar.text,\n                                fontWeight: \"400\",\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                textShadow: \"0 4px 12px rgba(0, 0, 0, 0.3)\",\n                                letterSpacing: \"-2px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"16px\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? \"\\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                        \" : hovered ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                          \" : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"\\n                          0 8px 32px rgba(0, 0, 0, 0.1),\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n                        \" : hovered ? \"\\n                            0 4px 16px rgba(0, 0, 0, 0.05),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n                          \" : \"none\",\n                                                transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-2px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        background: \"\\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\\n                          \",\n                                                        borderRadius: \"0 8px 8px 0\",\n                                                        boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"32px\",\n                                                        height: \"32px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"10px\",\n                                                        background: active ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                          \" : hovered ? \"\\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                            \" : \"transparent\",\n                                                        transition: \"all 0.3s ease\",\n                                                        transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 18,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            transition: \"all 0.3s ease\",\n                                                            filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        transition: \"all 0.3s ease\",\n                                                        letterSpacing: \"-0.2px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && !active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        right: \"12px\",\n                                                        width: \"6px\",\n                                                        height: \"6px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"\\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\\n                          \",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 16px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\",\n                            background: \"\\n            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\\n          \"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\",\n                                padding: \"12px 16px\",\n                                background: \"\\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n            \",\n                                borderRadius: \"16px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                boxShadow: \"\\n              0 8px 32px rgba(0, 0, 0, 0.1),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n            \"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        background: \"\\n                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n              \",\n                                        borderRadius: \"12px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        backdropFilter: \"blur(10px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: \"\\n                0 4px 16px rgba(0, 0, 0, 0.1),\\n                inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n              \"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-2px\",\n                                                right: \"-2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"\\n                  radial-gradient(circle, #00E676 0%, #00C853 100%)\\n                \",\n                                                border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.sidebar.text,\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"2px\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"Alex Chen\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.sidebar.textTertiary,\n                                                lineHeight: \"1.2\",\n                                                fontWeight: \"500\"\n                                            },\n                                            children: \"AI Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"20px\",\n                                        height: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        borderRadius: \"6px\",\n                                        background: \"\\n                linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n              \",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"12px\",\n                                            color: colors.sidebar.textSecondary,\n                                            transform: \"rotate(0deg)\",\n                                            transition: \"transform 0.3s ease\"\n                                        },\n                                        children: \"⌄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: \"\\n            0 32px 80px rgba(0, 0, 0, 0.12),\\n            0 8px 32px rgba(0, 0, 0, 0.08),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\\n            0 0 0 1px rgba(255, 107, 53, 0.1)\\n          \",\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: \"\\n              radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"20 0%, transparent 70%)\\n            \"),\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});