"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MeetingPage = ()=>{\n    _s();\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let isMounted = true;\n        const createRoom = async ()=>{\n            try {\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                if (isMounted) {\n                    setRoomUrl(data.url);\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                if (isMounted) {\n                    // Use a public Daily.co room that actually exists\n                    setRoomUrl(\"https://demo.daily.co/hello\");\n                    setLoading(false);\n                }\n            }\n        };\n        createRoom();\n        return ()=>{\n            isMounted = false;\n        };\n    }, []);\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show loading state while creating room\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show error if no room URL\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#dc3545\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Unable to create meeting room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Please try refreshing the page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        style: {\n                            backgroundColor: \"var(--accent-color)\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            padding: \"12px 24px\",\n                            fontSize: \"16px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Refresh Page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MeetingPage, \"3bBrBGo36FRf5a6kRNxNOBdE9KA=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});