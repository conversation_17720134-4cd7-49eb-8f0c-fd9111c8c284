"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MeetingPage = ()=>{\n    // Static room URL - no state, no effects, no re-renders\n    const roomUrl = \"https://demo.daily.co/hello\";\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show loading state while creating room\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show error if no room URL\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#dc3545\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Unable to create meeting room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Please try refreshing the page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        style: {\n                            backgroundColor: \"var(--accent-color)\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            padding: \"12px 24px\",\n                            fontSize: \"16px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Refresh Page\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});