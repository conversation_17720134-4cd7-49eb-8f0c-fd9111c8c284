"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Clean SaaS color palette - Light Sky Blue theme\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            muted: \"#CBD5E1\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\" // Primary colored border\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            border: \"#E2E8F0\",\n            hover: \"#F8FAFC\",\n            active: \"#F0F9FF\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: \"\\uD83C\\uDFE0\",\n            description: \"Dashboard & Analytics\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Analytics\",\n            icon: \"\\uD83D\\uDCCA\",\n            description: \"Data & Insights\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social Hub\",\n            icon: \"\\uD83D\\uDC26\",\n            description: \"Content & Engagement\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: \"\\uD83C\\uDFA5\",\n            description: \"Video & Collaboration\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"280px\",\n                    background: colors.sidebar.background,\n                    borderRight: \"1px solid \".concat(colors.sidebar.border),\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    boxShadow: \"0 0 0 1px rgba(0, 0, 0, 0.02), 0 4px 24px rgba(0, 0, 0, 0.04)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 24px 24px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light),\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.accent, \" 100%)\"),\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    background: \"\\n              radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"06 0%, transparent 50%),\\n              radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"04 0%, transparent 50%)\\n            \"),\n                                    pointerEvents: \"none\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"48px\",\n                                            height: \"48px\",\n                                            borderRadius: \"16px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginBottom: \"16px\",\n                                            boxShadow: \"\\n                0 8px 24px \".concat(colors.primary, \"30,\\n                0 0 0 1px \").concat(colors.primary, \"20,\\n                inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n              \")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"24px\",\n                                                color: colors.text.inverse,\n                                                fontWeight: \"700\",\n                                                letterSpacing: \"-0.5px\"\n                                            },\n                                            children: \"E\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"28px\",\n                                            fontWeight: \"800\",\n                                            letterSpacing: \"-1px\",\n                                            color: colors.text.primary,\n                                            lineHeight: \"1.2\",\n                                            marginBottom: \"4px\"\n                                        },\n                                        children: \"Exie\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"15px\",\n                                            color: colors.text.secondary,\n                                            fontWeight: \"500\",\n                                            letterSpacing: \"0.1px\"\n                                        },\n                                        children: \"AI-Powered Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"24px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 24px\"\n                            },\n                            children: menuItems.map((item)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"16px 20px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.3s cubic-bezier(0.16, 1, 0.3, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                backgroundColor: active ? colors.sidebar.active : hovered ? colors.sidebar.hover : \"transparent\",\n                                                border: active ? \"1px solid \".concat(colors.border.primary, \"30\") : \"1px solid transparent\",\n                                                boxShadow: active ? \"\\n                          0 0 0 1px \".concat(colors.border.primary, \"20,\\n                          0 4px 16px \").concat(colors.primary, \"15,\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n                        \") : hovered ? \"\\n                            0 0 0 1px \".concat(colors.border.light, \",\\n                            0 2px 8px rgba(0, 0, 0, 0.04),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.6)\\n                          \") : \"none\",\n                                                transform: hovered ? \"translateY(-1px)\" : \"translateY(0)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"0\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                                        borderRadius: \"0 4px 4px 0\",\n                                                        boxShadow: \"0 0 8px \".concat(colors.primary, \"40\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"40px\",\n                                                        height: \"40px\",\n                                                        borderRadius: \"10px\",\n                                                        background: active ? \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\") : hovered ? colors.surfaceElevated : colors.surfaceElevated,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"16px\",\n                                                        transition: \"all 0.3s ease\",\n                                                        boxShadow: active ? \"\\n                            0 4px 12px \".concat(colors.primary, \"30,\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n                          \") : \"\\n                            0 2px 6px rgba(0, 0, 0, 0.04),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.5)\\n                          \",\n                                                        transform: hovered ? \"scale(1.05)\" : \"scale(1)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"18px\",\n                                                            color: active ? colors.text.inverse : colors.text.secondary,\n                                                            transition: \"color 0.2s ease\"\n                                                        },\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"16px\",\n                                                                fontWeight: active ? \"700\" : \"600\",\n                                                                color: active ? colors.primary : colors.text.primary,\n                                                                marginBottom: \"2px\",\n                                                                transition: \"all 0.2s ease\",\n                                                                letterSpacing: \"-0.2px\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"13px\",\n                                                                color: colors.text.tertiary,\n                                                                opacity: hovered || active ? 1 : 0.8,\n                                                                transition: \"opacity 0.2s ease\",\n                                                                fontWeight: \"500\"\n                                                            },\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && !active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        borderRadius: \"6px\",\n                                                        background: \"linear-gradient(135deg, \".concat(colors.primary, \"20 0%, \").concat(colors.primaryLight, \"20 100%)\"),\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        transition: \"all 0.2s ease\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"10px\",\n                                                            color: colors.primary,\n                                                            fontWeight: \"600\"\n                                                        },\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px\",\n                            borderTop: \"1px solid \".concat(colors.border.light),\n                            marginTop: \"auto\",\n                            background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, \").concat(colors.accent, \" 100%)\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"20px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"8px\",\n                                        marginBottom: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                flex: 1,\n                                                padding: \"10px\",\n                                                border: \"1px solid \".concat(colors.border.light),\n                                                borderRadius: \"8px\",\n                                                backgroundColor: colors.surface,\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"14px\",\n                                                color: colors.text.secondary\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surfaceElevated;\n                                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surface;\n                                                e.currentTarget.style.transform = \"translateY(0)\";\n                                            },\n                                            children: \"⚙️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                flex: 1,\n                                                padding: \"10px\",\n                                                border: \"1px solid \".concat(colors.border.light),\n                                                borderRadius: \"8px\",\n                                                backgroundColor: colors.surface,\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"14px\",\n                                                color: colors.text.secondary,\n                                                position: \"relative\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surfaceElevated;\n                                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surface;\n                                                e.currentTarget.style.transform = \"translateY(0)\";\n                                            },\n                                            children: [\n                                                \"\\uD83D\\uDD14\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        top: \"6px\",\n                                                        right: \"6px\",\n                                                        width: \"8px\",\n                                                        height: \"8px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"#EF4444\",\n                                                        border: \"2px solid white\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                flex: 1,\n                                                padding: \"10px\",\n                                                border: \"1px solid \".concat(colors.border.light),\n                                                borderRadius: \"8px\",\n                                                backgroundColor: colors.surface,\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"14px\",\n                                                color: colors.text.secondary\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surfaceElevated;\n                                                e.currentTarget.style.transform = \"translateY(-1px)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.backgroundColor = colors.surface;\n                                                e.currentTarget.style.transform = \"translateY(0)\";\n                                            },\n                                            children: \"❓\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"16px\",\n                                    backgroundColor: colors.surface,\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid \".concat(colors.border.light),\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    boxShadow: \"\\n              0 2px 8px rgba(0, 0, 0, 0.04),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n            \"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(-1px)\";\n                                    e.currentTarget.style.boxShadow = \"\\n              0 4px 16px rgba(0, 0, 0, 0.08),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n            \";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.boxShadow = \"\\n              0 2px 8px rgba(0, 0, 0, 0.04),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n            \";\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"44px\",\n                                            height: \"44px\",\n                                            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, \").concat(colors.primaryLight, \" 100%)\"),\n                                            borderRadius: \"12px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            boxShadow: \"\\n                0 4px 12px \".concat(colors.primary, \"30,\\n                inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n              \"),\n                                            position: \"relative\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: \"white\",\n                                                    fontSize: \"18px\",\n                                                    fontWeight: \"700\",\n                                                    letterSpacing: \"-0.5px\"\n                                                },\n                                                children: \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-2px\",\n                                                    right: \"-2px\",\n                                                    width: \"12px\",\n                                                    height: \"12px\",\n                                                    borderRadius: \"50%\",\n                                                    background: \"#10B981\",\n                                                    border: \"2px solid white\",\n                                                    boxShadow: \"0 0 8px rgba(16, 185, 129, 0.4)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    fontWeight: \"700\",\n                                                    color: colors.text.primary,\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"2px\",\n                                                    letterSpacing: \"-0.2px\"\n                                                },\n                                                children: \"Alex Chen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"13px\",\n                                                    color: colors.text.tertiary,\n                                                    lineHeight: \"1.2\",\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: \"Account Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"24px\",\n                                            height: \"24px\",\n                                            borderRadius: \"6px\",\n                                            background: colors.surfaceElevated,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.text.tertiary,\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"⌄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"12px\",\n                                    padding: \"8px 12px\",\n                                    backgroundColor: \"\".concat(colors.primary, \"10\"),\n                                    borderRadius: \"8px\",\n                                    border: \"1px solid \".concat(colors.primary, \"20\"),\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"6px\",\n                                            height: \"6px\",\n                                            borderRadius: \"50%\",\n                                            background: colors.primary,\n                                            boxShadow: \"0 0 6px \".concat(colors.primary, \"60\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\",\n                                            color: colors.primary,\n                                            letterSpacing: \"0.2px\"\n                                        },\n                                        children: \"All systems operational\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.background,\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});