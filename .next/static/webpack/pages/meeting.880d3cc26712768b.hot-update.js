"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MeetingPage = ()=>{\n    // Use a static demo room URL to prevent the infinite loop\n    const roomUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: \"#f8f8f8\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"16px\",\n                    backgroundColor: \"white\",\n                    borderBottom: \"1px solid #e0e0e0\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            margin: 0,\n                            fontSize: \"20px\"\n                        },\n                        children: \"Exie AI Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"14px\",\n                            color: \"#666\"\n                        },\n                        children: \"AI-powered mentoring session with Daily.co\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                src: roomUrl,\n                allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                style: {\n                    width: \"100%\",\n                    height: \"100%\",\n                    border: \"none\",\n                    borderRadius: \"0 0 8px 8px\"\n                },\n                title: \"Daily.co Video Meeting\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your Daily.co meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"24px\",\n                    right: \"24px\",\n                    backgroundColor: \"var(--accent-color)\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"60px\",\n                    height: \"60px\",\n                    fontSize: \"24px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 4px 12px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.2s ease\",\n                    zIndex: 1000\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                    e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1)\";\n                    e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});