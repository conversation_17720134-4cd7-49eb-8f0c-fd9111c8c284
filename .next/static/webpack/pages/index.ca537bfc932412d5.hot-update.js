"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Overview\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Dashboard\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Insights\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Social\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"Content\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: \"\\n        radial-gradient(circle at 20% 20%, \".concat(colors.warmGlow, \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 80%, \").concat(colors.accent, \" 0%, transparent 50%),\\n        linear-gradient(135deg, \").concat(colors.background, \" 0%, \").concat(colors.accent, \" 100%)\\n      \"),\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"20px\",\n                    boxShadow: \"\\n          0 20px 60px \".concat(colors.sidebar.glow, \",\\n          0 8px 32px rgba(0, 0, 0, 0.15),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n        \"),\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"32px 24px\",\n                            borderBottom: \"1px solid \".concat(colors.sidebar.border),\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            position: \"relative\",\n                            background: \"\\n            radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\\n          \"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: \"50%\",\n                                    left: \"50%\",\n                                    transform: \"translate(-50%, -50%)\",\n                                    width: \"80px\",\n                                    height: \"80px\",\n                                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%)\",\n                                    borderRadius: \"50%\",\n                                    filter: \"blur(20px)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    zIndex: 1,\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    width: \"60px\",\n                                    height: \"60px\",\n                                    borderRadius: \"18px\",\n                                    background: \"\\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n            \",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"\\n              0 8px 32px rgba(0, 0, 0, 0.1),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n            \"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"28px\",\n                                        color: colors.sidebar.text,\n                                        fontWeight: \"400\",\n                                        fontFamily: \"Georgia, serif\",\n                                        fontStyle: \"italic\",\n                                        textShadow: \"0 2px 8px rgba(0, 0, 0, 0.2)\",\n                                        letterSpacing: \"-1px\"\n                                    },\n                                    children: \"ℰ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"16px\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? \"\\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                        \" : hovered ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                          \" : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? \"\\n                          0 8px 32px rgba(0, 0, 0, 0.1),\\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n                        \" : hovered ? \"\\n                            0 4px 16px rgba(0, 0, 0, 0.05),\\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\\n                          \" : \"none\",\n                                                transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-2px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"4px\",\n                                                        height: \"24px\",\n                                                        background: \"\\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\\n                          \",\n                                                        borderRadius: \"0 8px 8px 0\",\n                                                        boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"32px\",\n                                                        height: \"32px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"10px\",\n                                                        background: active ? \"\\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n                          \" : hovered ? \"\\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n                            \" : \"transparent\",\n                                                        transition: \"all 0.3s ease\",\n                                                        transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 18,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            transition: \"all 0.3s ease\",\n                                                            filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        transition: \"all 0.3s ease\",\n                                                        letterSpacing: \"-0.2px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                hovered && !active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        right: \"12px\",\n                                                        width: \"6px\",\n                                                        height: \"6px\",\n                                                        borderRadius: \"50%\",\n                                                        background: \"\\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\\n                          \",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 16px\",\n                            borderTop: \"1px solid \".concat(colors.sidebar.border),\n                            marginTop: \"auto\",\n                            background: \"\\n            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\\n          \"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\",\n                                padding: \"12px 16px\",\n                                background: \"\\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n            \",\n                                borderRadius: \"16px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                boxShadow: \"\\n              0 8px 32px rgba(0, 0, 0, 0.1),\\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\\n            \"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        background: \"\\n                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\\n              \",\n                                        borderRadius: \"12px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        backdropFilter: \"blur(10px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: \"\\n                0 4px 16px rgba(0, 0, 0, 0.1),\\n                inset 0 1px 0 rgba(255, 255, 255, 0.4)\\n              \"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-2px\",\n                                                right: \"-2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"\\n                  radial-gradient(circle, #00E676 0%, #00C853 100%)\\n                \",\n                                                border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.sidebar.text,\n                                                lineHeight: \"1.2\",\n                                                marginBottom: \"2px\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"Alex Chen\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.sidebar.textTertiary,\n                                                lineHeight: \"1.2\",\n                                                fontWeight: \"500\"\n                                            },\n                                            children: \"AI Manager\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"20px\",\n                                        height: \"20px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        borderRadius: \"6px\",\n                                        background: \"\\n                linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\\n              \",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: \"12px\",\n                                            color: colors.sidebar.textSecondary,\n                                            transform: \"rotate(0deg)\",\n                                            transition: \"transform 0.3s ease\"\n                                        },\n                                        children: \"⌄\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: \"\\n            0 32px 80px rgba(0, 0, 0, 0.12),\\n            0 8px 32px rgba(0, 0, 0, 0.08),\\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\\n            0 0 0 1px rgba(255, 107, 53, 0.1)\\n          \",\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: \"\\n              radial-gradient(ellipse at top, \".concat(colors.warmGlow, \"20 0%, transparent 70%)\\n            \"),\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"UxRSMYWsrFNuoZoTNmr04qwHNEs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});