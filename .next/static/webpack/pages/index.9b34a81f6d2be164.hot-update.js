"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"16px\",\n                    padding: \"24px\",\n                    marginBottom: \"24px\",\n                    boxShadow: \"0 4px 16px rgba(0, 0, 0, 0.04)\",\n                    border: \"1px solid \".concat(colors.border)\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                marginBottom: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"20px\",\n                                        fontWeight: \"600\"\n                                    },\n                                    children: \"Today's Mission\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        padding: \"4px 8px\",\n                                        background: \"\".concat(colors.primary, \"15\"),\n                                        borderRadius: \"6px\",\n                                        color: colors.primary,\n                                        fontSize: \"11px\",\n                                        fontWeight: \"600\",\n                                        textTransform: \"uppercase\",\n                                        letterSpacing: \"0.5px\"\n                                    },\n                                    children: \"AI Generated\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: colors.text.secondary,\n                                fontSize: \"16px\",\n                                lineHeight: \"1.5\",\n                                margin: 0,\n                                marginBottom: \"20px\"\n                            },\n                            children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"grid\",\n                                gridTemplateColumns: \"repeat(3, 1fr)\",\n                                gap: \"16px\",\n                                marginBottom: \"20px\"\n                            },\n                            children: [\n                                {\n                                    label: \"Engagement Rate\",\n                                    value: \"+24%\"\n                                },\n                                {\n                                    label: \"New Followers\",\n                                    value: \"127\"\n                                },\n                                {\n                                    label: \"Content Score\",\n                                    value: \"8.9/10\"\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"\".concat(colors.primary, \"08\"),\n                                        borderRadius: \"12px\",\n                                        padding: \"16px\",\n                                        textAlign: \"center\",\n                                        border: \"1px solid \".concat(colors.primary, \"15\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"20px\",\n                                                fontWeight: \"700\",\n                                                color: colors.text.primary,\n                                                marginBottom: \"4px\"\n                                            },\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"13px\",\n                                                color: colors.text.tertiary,\n                                                fontWeight: \"500\"\n                                            },\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"12px\",\n                                flexWrap: \"wrap\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/meeting\",\n                                    style: {\n                                        textDecoration: \"none\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"12px 20px\",\n                                            background: colors.primary,\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        children: \"Join Call\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        padding: \"12px 20px\",\n                                        background: colors.surface,\n                                        color: colors.text.primary,\n                                        border: \"1px solid \".concat(colors.border),\n                                        borderRadius: \"8px\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"600\",\n                                        cursor: \"pointer\",\n                                        transition: \"all 0.2s ease\"\n                                    },\n                                    children: \"Ask Mentor\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/tweet-center\",\n                                    style: {\n                                        textDecoration: \"none\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"12px 20px\",\n                                            background: colors.surface,\n                                            color: colors.text.primary,\n                                            border: \"1px solid \".concat(colors.border),\n                                            borderRadius: \"8px\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.2s ease\"\n                                        },\n                                        children: \"Create Content\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(135deg, \".concat(colors.surface, \" 0%, #FFE0B215 100%)\"),\n                    borderRadius: \"20px\",\n                    padding: \"24px\",\n                    boxShadow: \"\\n          0 12px 40px rgba(0, 0, 0, 0.06),\\n          inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n        \",\n                    border: \"1px solid \".concat(colors.border)\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"64px\",\n                                height: \"64px\",\n                                borderRadius: \"20px\",\n                                background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #FF8A65 100%)\"),\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: \"0 8px 24px \".concat(colors.primary, \"30\"),\n                                position: \"relative\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"32px\"\n                                    },\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"absolute\",\n                                        bottom: \"-2px\",\n                                        right: \"-2px\",\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        borderRadius: \"50%\",\n                                        background: \"#00E676\",\n                                        border: \"2px solid white\",\n                                        boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"18px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"4px\"\n                                    },\n                                    children: \"AI Mentor\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: '\"Ready to help you create content that resonates. What\\'s on your mind today?\"'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HomePage;\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});