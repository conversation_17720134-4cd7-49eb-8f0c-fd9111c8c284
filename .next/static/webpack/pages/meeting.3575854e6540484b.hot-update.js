"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @daily-co/daily-react */ \"./node_modules/@daily-co/daily-react/dist/daily-react.esm.js\");\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MeetingPage = ()=>{\n    _s();\n    // Bring back the state for controls and Exie interaction\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [micOn, setMicOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [cameraOn, setCameraOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [exieLoading, setExieLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exieVideoUrl, setExieVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Exie's video URL\n    const [exieTranscript, setExieTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Exie's transcript\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setJoined(false); // Start as not joined, will join when user clicks join\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setJoined(false);\n            }\n        };\n        createRoom();\n    }, []); // Run once on component mount\n    // Handlers for controls - Implement the logic here\n    const handleToggleMic = async ()=>{\n        // Toggle mic logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setMicOn(!micOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the micOn prop.\n    };\n    const handleToggleCamera = async ()=>{\n        // Toggle camera logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setCameraOn(!cameraOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the cameraOn prop.\n    };\n    const handleJoin = async ()=>{\n        setJoined(true);\n    };\n    const handleLeave = async ()=>{\n        setJoined(false);\n    // Optionally reload to reset state\n    // window.location.reload();\n    };\n    // Handle Talk to Exie logic (re-implemented here)\n    const handleTalkToExie = async ()=>{\n        if (!micOn || exieLoading) return; // Ensure mic state is on and not already loading\n        setExieLoading(true);\n        setExieTranscript(\"\"); // Clear previous transcript\n        setExieVideoUrl(null); // Clear previous video\n        console.log(\"Starting audio recording...\");\n        let mediaRecorder = null;\n        let stream = null;\n        try {\n            // Get a new audio stream specifically for recording\n            stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm\"\n            });\n            const audioChunks = [];\n            mediaRecorder.ondataavailable = (e)=>{\n                if (e.data.size > 0) {\n                    audioChunks.push(e.data);\n                    console.log(\"Collected audio chunk:\", e.data.size, \"bytes\");\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                console.log(\"Audio recording stopped. Total chunks:\", audioChunks.length);\n                const audioBlob = new Blob(audioChunks, {\n                    type: \"audio/webm\"\n                });\n                console.log(\"Audio Blob created:\", audioBlob.size, \"bytes\", audioBlob.type);\n                // Send to /api/transcribe\n                console.log(\"Sending audio to /api/transcribe...\");\n                const res = await fetch(\"/api/transcribe\", {\n                    method: \"POST\",\n                    body: audioBlob,\n                    headers: {\n                        \"Content-Type\": \"audio/webm\"\n                    }\n                });\n                if (!res.ok) throw new Error(\"Transcribe API error: \".concat(res.status));\n                const data = await res.json();\n                const transcript = data.transcript || \"\";\n                console.log(\"Transcript received:\", transcript);\n                setExieTranscript(transcript); // Update Exie\\'s transcript state\n                if (transcript) {\n                    var _voiceData_audioBase64;\n                    // Send to AI pipeline (gpt -> voice -> video)\n                    console.log(\"Sending transcript to /api/gpt...\");\n                    const gptRes = await fetch(\"/api/gpt\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            message: transcript,\n                            tweetsOrHandle: \"\"\n                        })\n                    });\n                    if (!gptRes.ok) throw new Error(\"GPT API error: \".concat(gptRes.status));\n                    const gptData = await gptRes.json();\n                    const mentorReply = gptData.reply || \"Sorry, I could not generate a response.\";\n                    console.log(\"GPT Reply received:\", mentorReply);\n                    setExieTranscript(mentorReply); // Update Exie\\'s transcript state with reply\n                    // ElevenLabs\n                    console.log(\"Sending text to /api/voice...\");\n                    const voiceRes = await fetch(\"/api/voice\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            text: mentorReply\n                        })\n                    });\n                    if (!voiceRes.ok) throw new Error(\"Voice API error: \".concat(voiceRes.status));\n                    const voiceData = await voiceRes.json();\n                    console.log(\"Voice data received (base64 length):\", (_voiceData_audioBase64 = voiceData.audioBase64) === null || _voiceData_audioBase64 === void 0 ? void 0 : _voiceData_audioBase64.length);\n                    if (!voiceData.audioBase64) throw new Error(\"No audio returned from ElevenLabs.\");\n                    // Create a blob URL for the audio\n                    const audioBlob = new Blob([\n                        Uint8Array.from(atob(voiceData.audioBase64), (c)=>c.charCodeAt(0))\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioBlob);\n                    console.log(\"Audio Blob URL created:\", audioUrl);\n                    // D-ID\n                    console.log(\"Sending audio URL to /api/video...\");\n                    const videoRes = await fetch(\"/api/video\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            audioUrl,\n                            imageUrl: \"https://via.placeholder.com/320x240.png?text=Exie+AI\"\n                        }) // Use consistent placeholder\n                    });\n                    if (!videoRes.ok) throw new Error(\"Video API error: \".concat(videoRes.status));\n                    const videoData = await videoRes.json();\n                    console.log(\"Video URL received:\", videoData.videoUrl);\n                    if (videoData.videoUrl) setExieVideoUrl(videoData.videoUrl); // Update Exie\\'s video URL state\n                    // Play the audio\n                    const audio = new Audio(audioUrl);\n                    audio.play();\n                } else {\n                    console.log(\"No transcript to process.\");\n                }\n            };\n            mediaRecorder.start();\n            // Stop recording after a short delay (adjust as needed)\n            setTimeout(()=>{\n                if ((mediaRecorder === null || mediaRecorder === void 0 ? void 0 : mediaRecorder.state) !== \"inactive\") mediaRecorder === null || mediaRecorder === void 0 ? void 0 : mediaRecorder.stop();\n                // Stop the getUserMedia tracks after recording stops\n                stream === null || stream === void 0 ? void 0 : stream.getTracks().forEach((track)=>track.stop());\n            }, 5000); // Record for 5 seconds max\n        } catch (error) {\n            console.error(\"Error recording audio or processing AI pipeline:\", error);\n            setExieTranscript(\"Sorry, there was an error processing your request.\"); // Update Exie\\'s transcript state on error\n        } finally{\n            setExieLoading(false);\n        }\n    };\n    // Effect to join the Agora channel when component mounts and agoraClient is ready (now handled in VideoCallGrid)\n    // However, we need to set \\'joined\\' state based on connection status.\n    // We\\'ll rely on VideoCallGrid to emit a \\'joined\\' status change or similar if needed, or manage joining entirely in VideoCallGrid\\'s hook.\n    // For simplicity now, assume VideoCallGrid\\'s useJoin hook is sufficient to manage the connection, and we\\'ll rely on the mic/camera tracks being ready to indicate a potential \\'joined\\' state for controls visibility.\n    // A more robust approach might involve a context or callback from VideoCallGrid when truly connected.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simple way to set joined state based on whether local tracks are available\n        if (micOn && cameraOn) {\n        // This is a simplification. A real \\'joined\\' status should come from Agora\\'s connection state.\n        // We will pass the \\'joined\\' prop down, and VideoCallGrid\\'s useJoin will attempt to join.\n        // We might need a mechanism to get the actual connection state back up from VideoCallGrid.\n        // For now, let\\'s rely on VideoCallGrid\\'s internal joined state derived from the Agora client listener\n        // and pass down the mic/camera state and handlers.\n        // The \\'joined\\' state in MeetingPage will primarily control visibility of the footer controls.\n        // Re-add connection state listener if not fully handled by VideoCallGrid\n        // If VideoCallGrid\\'s useJoin manages connection and updates an internal state,\n        // we might need a way for VideoCallGrid to communicate the joined status up.\n        // Let\\'s stick to the current plan: VideoCallGrid handles Agora, MeetingPage handles overall UI and AI pipeline.\n        // MeetingPage\\'s \\'joined\\' state will primarily control visibility of the footer controls.\n        // A potential approach: VideoCallGrid calls a prop function like onJoinedStatusChange(status: boolean)\n        // useEffect(() => {\n        //   if (agoraClient) {\n        //     agoraClient.on(\\'connection-state-change\\', (state) => {\n        //       if (state === \\'CONNECTED\\') {\n        //         setJoined(true);\n        //       } else {\n        //         setJoined(false);\n        //       }\n        //     });\n        //   }\n        // }, [agoraClient]);\n        // For Daily.co, the useRoom hook\\'s state might be a better indicator of joined status.\n        // Let\\'s update the joined state based on the presence of a room URL for now, as done in the initial effect.\n        }\n    }, [\n        micOn,\n        cameraOn\n    ]); // Depend on micOn and cameraOn to re-check joined status (simplified)\n    // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined);\n    }\n    return(// Wrap the meeting content in DailyProvider to provide the Daily room context to children\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyProvider, {\n        url: roomUrl,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: [\n                \" \",\n                !joined ? // Show join screen when not joined\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: \"100vh\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        flexDirection: \"column\",\n                        gap: \"24px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"var(--accent-color)\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ready to meet with Exie?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: \"#666\",\n                                    marginBottom: \"32px\"\n                                },\n                                children: \"Join the meeting to start your AI mentoring session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoin,\n                                style: {\n                                    backgroundColor: \"var(--accent-color)\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    padding: \"12px 24px\",\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.opacity = \"0.9\",\n                                onMouseOut: (e)=>e.currentTarget.style.opacity = \"1\",\n                                children: \"Join Meeting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoCallGrid, {\n                            joined: joined,\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            exieLoading: exieLoading,\n                            exieVideoUrl: exieVideoUrl,\n                            exieTranscript: exieTranscript,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MeetingControls, {\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie,\n                            exieLoading: exieLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 252,\n            columnNumber: 8\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, undefined));\n};\n_s(MeetingPage, \"3iEaDbr8FwW7oQDt8Dd7ZGy125I=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});