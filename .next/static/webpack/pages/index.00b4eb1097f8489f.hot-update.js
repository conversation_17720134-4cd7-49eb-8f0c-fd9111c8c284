"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"daily-operation\": true,\n        \"manage-staff\": false,\n        \"manage-guests\": true\n    });\n    // Fixoria-inspired color palette\n    const colors = {\n        primary: \"#22C55E\",\n        primaryLight: \"#4ADE80\",\n        primaryDark: \"#16A34A\",\n        accent: \"#F0FDF4\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFAFA\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\",\n            primary: \"#22C55E\" // Primary colored border\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            border: \"#E5E7EB\",\n            hover: \"#F9FAFB\",\n            active: \"#F0FDF4\"\n        }\n    };\n    const navigationSections = [\n        {\n            id: \"daily-operation\",\n            title: \"DAILY OPERATION\",\n            items: [\n                {\n                    href: \"/dashboard\",\n                    label: \"Dashboard\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    isActive: router.pathname === \"/dashboard\"\n                },\n                {\n                    href: \"/reservation\",\n                    label: \"Reservation\",\n                    icon: \"\\uD83D\\uDCC5\",\n                    isActive: router.pathname.startsWith(\"/reservation\"),\n                    hasSubmenu: true\n                },\n                {\n                    href: \"/room-operation\",\n                    label: \"Room Operation\",\n                    icon: \"\\uD83C\\uDFE0\",\n                    isActive: router.pathname === \"/room-operation\"\n                }\n            ]\n        },\n        {\n            id: \"manage-staff\",\n            title: \"MANAGE STAFF\",\n            items: [\n                {\n                    href: \"/manage-staff\",\n                    label: \"Manage Staff\",\n                    icon: \"\\uD83D\\uDC65\",\n                    isActive: router.pathname.startsWith(\"/manage-staff\"),\n                    hasSubmenu: true\n                }\n            ]\n        },\n        {\n            id: \"manage-guests\",\n            title: \"MANAGE GUESTS\",\n            items: [\n                {\n                    href: \"/manage-guests\",\n                    label: \"Manage Guests\",\n                    icon: \"\\uD83D\\uDC64\",\n                    isActive: router.pathname.startsWith(\"/manage-guests\"),\n                    hasSubmenu: true,\n                    subItems: [\n                        {\n                            href: \"/manage-guests/guests-list\",\n                            label: \"Guests List\",\n                            isActive: router.pathname === \"/manage-guests/guests-list\" || router.pathname === \"/meeting\"\n                        },\n                        {\n                            href: \"/manage-guests/reviews\",\n                            label: \"Guests Reviews\",\n                            isActive: router.pathname === \"/manage-guests/reviews\"\n                        }\n                    ]\n                }\n            ]\n        }\n    ];\n    const bottomSections = [\n        {\n            href: \"/promotions\",\n            label: \"Promotions\",\n            icon: \"\\uD83C\\uDFAF\",\n            isActive: router.pathname === \"/promotions\"\n        }\n    ];\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [sectionId]: !prev[sectionId]\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    borderRight: \"1px solid \".concat(colors.sidebar.border),\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light),\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"12px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"32px\",\n                                    height: \"32px\",\n                                    background: colors.primary,\n                                    borderRadius: \"6px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    position: \"relative\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"700\"\n                                    },\n                                    children: \"F\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: colors.text.primary,\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: \"Fixoria ™\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: \"auto\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    background: colors.surfaceElevated,\n                                    borderRadius: \"4px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    cursor: \"pointer\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⚙\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: \"1px solid \".concat(colors.border.light)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"24px\",\n                                        height: \"24px\",\n                                        background: colors.primary,\n                                        borderRadius: \"50%\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.text.primary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Grand Sylhet Hotel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.text.tertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"3 more hotels\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⌄\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"0\",\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            navigationSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"24px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onClick: ()=>toggleSection(section.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"11px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.tertiary,\n                                                        letterSpacing: \"0.5px\",\n                                                        textTransform: \"uppercase\"\n                                                    },\n                                                    children: section.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        transform: expandedSections[section.id] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.2s ease\"\n                                                    },\n                                                    children: \"⌄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        expandedSections[section.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: \"8px\",\n                                                paddingRight: \"8px\"\n                                            },\n                                            children: section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: item.href,\n                                                            style: {\n                                                                textDecoration: \"none\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    padding: \"8px 12px\",\n                                                                    margin: \"2px 0\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                                    transition: \"all 0.15s ease\"\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                    }\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"16px\",\n                                                                            marginRight: \"12px\",\n                                                                            width: \"20px\",\n                                                                            textAlign: \"center\"\n                                                                        },\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"14px\",\n                                                                            fontWeight: item.isActive ? \"600\" : \"500\",\n                                                                            color: item.isActive ? colors.text.primary : colors.text.secondary,\n                                                                            flex: 1\n                                                                        },\n                                                                        children: item.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    item.hasSubmenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"12px\",\n                                                                            color: colors.text.tertiary,\n                                                                            transform: item.subItems && expandedSections[\"\".concat(section.id, \"-\").concat(item.href)] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                                            transition: \"transform 0.2s ease\"\n                                                                        },\n                                                                        children: \"⌄\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        item.subItems && item.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                paddingLeft: \"32px\",\n                                                                marginTop: \"4px\",\n                                                                marginBottom: \"8px\"\n                                                            },\n                                                            children: item.subItems.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: subItem.href,\n                                                                    style: {\n                                                                        textDecoration: \"none\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            padding: \"6px 12px\",\n                                                                            margin: \"1px 0\",\n                                                                            borderRadius: \"4px\",\n                                                                            cursor: \"pointer\",\n                                                                            backgroundColor: subItem.isActive ? colors.sidebar.active : \"transparent\",\n                                                                            borderLeft: subItem.isActive ? \"2px solid \".concat(colors.primary) : \"2px solid transparent\",\n                                                                            transition: \"all 0.15s ease\"\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                            }\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                            }\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: subItem.isActive ? \"600\" : \"500\",\n                                                                                color: subItem.isActive ? colors.text.primary : colors.text.secondary\n                                                                            },\n                                                                            children: subItem.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, subItem.href, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, item.href, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, section.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"8px\"\n                                },\n                                children: bottomSections.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"8px 12px\",\n                                                margin: \"2px 0\",\n                                                borderRadius: \"6px\",\n                                                cursor: \"pointer\",\n                                                backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                transition: \"all 0.15s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"16px\",\n                                                        marginRight: \"12px\",\n                                                        width: \"20px\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: item.isActive ? \"600\" : \"500\",\n                                                        color: item.isActive ? colors.text.primary : colors.text.secondary\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderTop: \"1px solid \".concat(colors.border.light),\n                            marginTop: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"ACCOUNTING\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"SYSTEM OPTIONS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/notifications\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"\\uD83D\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary,\n                                                flex: 1\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#EF4444\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"11px\",\n                                                color: \"white\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/support\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"❓\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary\n                                            },\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"12px 8px\",\n                                    marginTop: \"8px\",\n                                    cursor: \"pointer\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: colors.primary,\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"white\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.primary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Rahat Ali\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.text.tertiary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Super Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.background,\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 619,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SidebarLayout, \"VCfYwMbh02XTMin+trdDom6QSPc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ })

});