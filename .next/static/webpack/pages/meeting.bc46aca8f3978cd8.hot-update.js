"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @daily-co/daily-react */ \"./node_modules/@daily-co/daily-react/dist/daily-react.esm.js\");\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MeetingPage = ()=>{\n    _s();\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setLoading(false);\n            }\n        };\n        createRoom();\n    }, []); // Run once on component mount\n    // Render loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    return(// Wrap the meeting content in DailyProvider to provide the Daily room context to children\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyProvider, {\n        url: roomUrl,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: [\n                \" \",\n                !joined ? // Show join screen when not joined\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: \"100vh\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        flexDirection: \"column\",\n                        gap: \"24px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"var(--accent-color)\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ready to meet with Exie?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: \"#666\",\n                                    marginBottom: \"32px\"\n                                },\n                                children: \"Join the meeting to start your AI mentoring session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoin,\n                                style: {\n                                    backgroundColor: \"var(--accent-color)\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    padding: \"12px 24px\",\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.opacity = \"0.9\",\n                                onMouseOut: (e)=>e.currentTarget.style.opacity = \"1\",\n                                children: \"Join Meeting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoCallGrid, {\n                            joined: joined,\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            exieLoading: exieLoading,\n                            exieVideoUrl: exieVideoUrl,\n                            exieTranscript: exieTranscript,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MeetingControls, {\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie,\n                            exieLoading: exieLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 89,\n            columnNumber: 8\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined));\n};\n_s(MeetingPage, \"3bBrBGo36FRf5a6kRNxNOBdE9KA=\");\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});