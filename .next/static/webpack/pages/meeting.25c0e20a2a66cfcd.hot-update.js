"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/meeting",{

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MeetingPage = ()=>{\n    // Premium color palette - matching sidebar\n    const colors = {\n        primary: \"#0EA5E9\",\n        primaryLight: \"#38BDF8\",\n        primaryDark: \"#0284C7\",\n        accent: \"#F0F9FF\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFBFC\",\n        text: {\n            primary: \"#0F172A\",\n            secondary: \"#475569\",\n            tertiary: \"#94A3B8\",\n            inverse: \"#FFFFFF\"\n        },\n        border: {\n            light: \"#E2E8F0\",\n            medium: \"#CBD5E1\",\n            primary: \"#0EA5E9\"\n        },\n        shadow: {\n            subtle: \"rgba(15, 23, 42, 0.04)\",\n            medium: \"rgba(15, 23, 42, 0.08)\",\n            strong: \"rgba(15, 23, 42, 0.12)\",\n            primary: \"rgba(14, 165, 233, 0.15)\"\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            backgroundColor: colors.surfaceElevated,\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"32px 40px\",\n                    backgroundColor: colors.surface,\n                    borderBottom: \"1px solid \".concat(colors.border.light),\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    boxShadow: \"\\n          0 0 0 1px \".concat(colors.border.light, \",\\n          0 4px 24px \").concat(colors.shadow.subtle, \"\\n        \"),\n                    position: \"relative\",\n                    zIndex: 10\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: \"\\n            radial-gradient(circle at 20% 20%, \".concat(colors.primary, \"04 0%, transparent 50%),\\n            radial-gradient(circle at 80% 80%, \").concat(colors.primaryLight, \"03 0%, transparent 50%)\\n          \"),\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    margin: 0,\n                                    fontSize: \"32px\",\n                                    fontWeight: \"800\",\n                                    letterSpacing: \"-1px\",\n                                    lineHeight: \"1.2\"\n                                },\n                                children: \"AI Conference Suite\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: \"8px 0 0 0\",\n                                    fontSize: \"16px\",\n                                    color: colors.text.secondary,\n                                    fontWeight: \"500\",\n                                    letterSpacing: \"0.1px\"\n                                },\n                                children: \"Enterprise-grade video collaboration with AI assistance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"16px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: \"linear-gradient(135deg, \".concat(colors.accent, \" 0%, \").concat(colors.surface, \" 100%)\"),\n                                border: \"1px solid \".concat(colors.border.primary, \"40\"),\n                                padding: \"12px 20px\",\n                                borderRadius: \"16px\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"12px\",\n                                boxShadow: \"\\n              0 4px 16px \".concat(colors.shadow.primary, \",\\n              inset 0 1px 0 rgba(255, 255, 255, 0.8)\\n            \")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"8px\",\n                                        height: \"8px\",\n                                        borderRadius: \"50%\",\n                                        background: \"linear-gradient(135deg, #10B981 0%, #34D399 100%)\",\n                                        boxShadow: \"0 0 8px rgba(16, 185, 129, 0.4)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        fontSize: \"15px\",\n                                        fontWeight: \"700\",\n                                        letterSpacing: \"-0.1px\"\n                                    },\n                                    children: \"Live Session\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: \"24px\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        backgroundColor: \"white\",\n                        borderRadius: \"12px\",\n                        overflow: \"hidden\",\n                        boxShadow: \"0 4px 12px rgba(0,0,0,0.08)\",\n                        border: \"1px solid #e9ecef\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                        src: \"https://demo.daily.co/hello\",\n                        allow: \"camera; microphone; fullscreen; display-capture; autoplay\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            border: \"none\"\n                        },\n                        title: \"Daily.co Video Meeting\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>alert(\"AI Assistant integration coming soon! This will allow you to talk to Exie during your meeting.\"),\n                style: {\n                    position: \"absolute\",\n                    bottom: \"32px\",\n                    right: \"32px\",\n                    backgroundColor: \"#007bff\",\n                    color: \"white\",\n                    border: \"none\",\n                    borderRadius: \"50%\",\n                    width: \"64px\",\n                    height: \"64px\",\n                    fontSize: \"28px\",\n                    cursor: \"pointer\",\n                    boxShadow: \"0 8px 24px rgba(0, 123, 255, 0.3)\",\n                    transition: \"all 0.3s ease\",\n                    zIndex: 1000,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                },\n                onMouseOver: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1.1) translateY(-2px)\";\n                    e.currentTarget.style.boxShadow = \"0 12px 32px rgba(0, 123, 255, 0.4)\";\n                },\n                onMouseOut: (e)=>{\n                    e.currentTarget.style.transform = \"scale(1) translateY(0)\";\n                    e.currentTarget.style.boxShadow = \"0 8px 24px rgba(0, 123, 255, 0.3)\";\n                },\n                title: \"Talk to Exie AI\",\n                children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MeetingPage;\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (MeetingPage);\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n"));

/***/ })

});