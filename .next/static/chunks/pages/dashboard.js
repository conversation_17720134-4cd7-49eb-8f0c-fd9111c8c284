/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/dashboard"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdashboard.tsx&page=%2Fdashboard!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdashboard.tsx&page=%2Fdashboard! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/dashboard\",\n      function () {\n        return __webpack_require__(/*! ./pages/dashboard.tsx */ \"./pages/dashboard.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/dashboard\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZVc2VycyUyRmNhbGVsYW5lJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGRXhpZSUyRnBhZ2VzJTJGZGFzaGJvYXJkLnRzeCZwYWdlPSUyRmRhc2hib2FyZCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxvREFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2Q0YzciXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9kYXNoYm9hcmRcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3BhZ2VzL2Rhc2hib2FyZC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2Rhc2hib2FyZFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdashboard.tsx&page=%2Fdashboard!\n"));

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SidebarLayout = (param)=>{\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    backgroundColor: \"#f0f0f0\",\n                    padding: \"16px\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            listStyle: \"none\",\n                            padding: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/tweet-center\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Tweet Center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/meeting\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"AI Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    padding: \"24px\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SidebarLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SidebarLayout);\nvar _c;\n$RefreshReg$(_c, \"SidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlhQSxxQkFBbUI7ZUFBbkJBOztJQUpBQyxpQkFBZTtlQUFmQTs7SUFHQUMsaUJBQWU7ZUFBZkE7O0lBSkFDLGdCQUFjO2VBQWRBOztJQUVBQyxnQkFBYztlQUFkQTs7SUFJQUMsc0JBQW9CO2VBQXBCQTs7SUFIQUMscUJBQW1CO2VBQW5CQTs7Ozs7Ozs7SUF1UUdDLFlBQVU7ZUFBVkE7OztBQTFRVCxNQUFNSixpQkFBaUI7QUFDdkIsTUFBTUYsa0JBQWtCO0FBQ3hCLE1BQU1HLGlCQUFpQjtBQUN2QixNQUFNRSxzQkFBc0I7QUFDNUIsTUFBTUosa0JBQWtCO0FBQ3hCLE1BQU1GLHNCQUFzQjtBQUM1QixNQUFNSyx1QkFBdUI7O1VBdUl4QkcsWUFBQUE7Ozs7R0FBQUEsZ0JBQUFBLENBQUFBLGVBQUFBLENBQUFBLENBQUFBOztVQThEQUMsd0JBQUFBOzs7OztHQUFBQSw0QkFBQUEsQ0FBQUEsMkJBQUFBLENBQUFBLENBQUFBO0FBK0RMLFNBQVNGLFdBQVdHLEtBQVU7SUFDbkMsNkVBQTZFO0lBQzdFLDZFQUE2RTtJQUM3RSx1RUFBdUU7SUFDdkUsZ0JBQWdCO0lBQ2hCLE9BQ0VBLFNBQ0MsUUFBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsZUFDL0MsT0FBT0EsTUFBTUMsSUFBSSxLQUFLO0FBRTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMudHM/ZWYxYyJdLCJuYW1lcyI6WyJBQ1RJT05fRkFTVF9SRUZSRVNIIiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1BSRUZFVENIIiwiQUNUSU9OX1JFRlJFU0giLCJBQ1RJT05fUkVTVE9SRSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiQUNUSU9OX1NFUlZFUl9QQVRDSCIsImlzVGhlbmFibGUiLCJQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJ2YWx1ZSIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./pages/dashboard.tsx":
/*!*****************************!*\
  !*** ./pages/dashboard.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/dashboard.tsx\n\n\n\nconst DashboardPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"var(--accent-color)\",\n                    marginBottom: \"24px\"\n                },\n                children: \"Dashboard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n                    gap: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Quick Stats\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Meetings: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"150\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 62\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Tweets Sent: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 65\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Average Meeting Duration: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"30 mins\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 40\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Meeting with John Doe ended.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Tweet about new feature sent.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Scheduled meeting with Jane Smith.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = DashboardPage;\nDashboardPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (DashboardPage);\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard.tsx\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fcalelane%2FDocuments%2FGitHub%2FExie%2Fpages%2Fdashboard.tsx&page=%2Fdashboard!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);