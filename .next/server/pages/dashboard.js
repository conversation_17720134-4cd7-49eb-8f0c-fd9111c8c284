/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard";
exports.ids = ["pages/dashboard"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/dashboard.tsx */ \"./pages/dashboard.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"daily-operation\": true,\n        \"manage-staff\": false,\n        \"manage-guests\": true\n    });\n    // Fixoria-inspired color palette\n    const colors = {\n        primary: \"#22C55E\",\n        primaryLight: \"#4ADE80\",\n        primaryDark: \"#16A34A\",\n        accent: \"#F0FDF4\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFAFA\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\",\n            primary: \"#22C55E\" // Primary colored border\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            border: \"#E5E7EB\",\n            hover: \"#F9FAFB\",\n            active: \"#F0FDF4\"\n        }\n    };\n    const navigationSections = [\n        {\n            id: \"daily-operation\",\n            title: \"DAILY OPERATION\",\n            items: [\n                {\n                    href: \"/dashboard\",\n                    label: \"Dashboard\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    isActive: router.pathname === \"/dashboard\"\n                },\n                {\n                    href: \"/reservation\",\n                    label: \"Reservation\",\n                    icon: \"\\uD83D\\uDCC5\",\n                    isActive: router.pathname.startsWith(\"/reservation\"),\n                    hasSubmenu: true\n                },\n                {\n                    href: \"/room-operation\",\n                    label: \"Room Operation\",\n                    icon: \"\\uD83C\\uDFE0\",\n                    isActive: router.pathname === \"/room-operation\"\n                }\n            ]\n        },\n        {\n            id: \"manage-staff\",\n            title: \"MANAGE STAFF\",\n            items: [\n                {\n                    href: \"/manage-staff\",\n                    label: \"Manage Staff\",\n                    icon: \"\\uD83D\\uDC65\",\n                    isActive: router.pathname.startsWith(\"/manage-staff\"),\n                    hasSubmenu: true\n                }\n            ]\n        },\n        {\n            id: \"manage-guests\",\n            title: \"MANAGE GUESTS\",\n            items: [\n                {\n                    href: \"/manage-guests\",\n                    label: \"Manage Guests\",\n                    icon: \"\\uD83D\\uDC64\",\n                    isActive: router.pathname.startsWith(\"/manage-guests\"),\n                    hasSubmenu: true,\n                    subItems: [\n                        {\n                            href: \"/manage-guests/guests-list\",\n                            label: \"Guests List\",\n                            isActive: router.pathname === \"/manage-guests/guests-list\" || router.pathname === \"/meeting\"\n                        },\n                        {\n                            href: \"/manage-guests/reviews\",\n                            label: \"Guests Reviews\",\n                            isActive: router.pathname === \"/manage-guests/reviews\"\n                        }\n                    ]\n                }\n            ]\n        }\n    ];\n    const bottomSections = [\n        {\n            href: \"/promotions\",\n            label: \"Promotions\",\n            icon: \"\\uD83C\\uDFAF\",\n            isActive: router.pathname === \"/promotions\"\n        }\n    ];\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [sectionId]: !prev[sectionId]\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    borderRight: `1px solid ${colors.sidebar.border}`,\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: `1px solid ${colors.border.light}`,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"12px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"32px\",\n                                    height: \"32px\",\n                                    background: colors.primary,\n                                    borderRadius: \"6px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    position: \"relative\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"700\"\n                                    },\n                                    children: \"F\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: colors.text.primary,\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: \"Fixoria ™\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: \"auto\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    background: colors.surfaceElevated,\n                                    borderRadius: \"4px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    cursor: \"pointer\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⚙\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: `1px solid ${colors.border.light}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"24px\",\n                                        height: \"24px\",\n                                        background: colors.primary,\n                                        borderRadius: \"50%\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.text.primary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Grand Sylhet Hotel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.text.tertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"3 more hotels\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⌄\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"0\",\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            navigationSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"24px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onClick: ()=>toggleSection(section.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"11px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.tertiary,\n                                                        letterSpacing: \"0.5px\",\n                                                        textTransform: \"uppercase\"\n                                                    },\n                                                    children: section.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        transform: expandedSections[section.id] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.2s ease\"\n                                                    },\n                                                    children: \"⌄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        expandedSections[section.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: \"8px\",\n                                                paddingRight: \"8px\"\n                                            },\n                                            children: section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: item.href,\n                                                            style: {\n                                                                textDecoration: \"none\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    padding: \"8px 12px\",\n                                                                    margin: \"2px 0\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                                    transition: \"all 0.15s ease\"\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                    }\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"16px\",\n                                                                            marginRight: \"12px\",\n                                                                            width: \"20px\",\n                                                                            textAlign: \"center\"\n                                                                        },\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"14px\",\n                                                                            fontWeight: item.isActive ? \"600\" : \"500\",\n                                                                            color: item.isActive ? colors.text.primary : colors.text.secondary,\n                                                                            flex: 1\n                                                                        },\n                                                                        children: item.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    item.hasSubmenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"12px\",\n                                                                            color: colors.text.tertiary,\n                                                                            transform: item.subItems && expandedSections[`${section.id}-${item.href}`] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                                            transition: \"transform 0.2s ease\"\n                                                                        },\n                                                                        children: \"⌄\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        item.subItems && item.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                paddingLeft: \"32px\",\n                                                                marginTop: \"4px\",\n                                                                marginBottom: \"8px\"\n                                                            },\n                                                            children: item.subItems.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: subItem.href,\n                                                                    style: {\n                                                                        textDecoration: \"none\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            padding: \"6px 12px\",\n                                                                            margin: \"1px 0\",\n                                                                            borderRadius: \"4px\",\n                                                                            cursor: \"pointer\",\n                                                                            backgroundColor: subItem.isActive ? colors.sidebar.active : \"transparent\",\n                                                                            borderLeft: subItem.isActive ? `2px solid ${colors.primary}` : \"2px solid transparent\",\n                                                                            transition: \"all 0.15s ease\"\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                            }\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                            }\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: subItem.isActive ? \"600\" : \"500\",\n                                                                                color: subItem.isActive ? colors.text.primary : colors.text.secondary\n                                                                            },\n                                                                            children: subItem.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, subItem.href, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, item.href, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, section.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"8px\"\n                                },\n                                children: bottomSections.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"8px 12px\",\n                                                margin: \"2px 0\",\n                                                borderRadius: \"6px\",\n                                                cursor: \"pointer\",\n                                                backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                transition: \"all 0.15s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"16px\",\n                                                        marginRight: \"12px\",\n                                                        width: \"20px\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: item.isActive ? \"600\" : \"500\",\n                                                        color: item.isActive ? colors.text.primary : colors.text.secondary\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderTop: `1px solid ${colors.border.light}`,\n                            marginTop: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"ACCOUNTING\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"SYSTEM OPTIONS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/notifications\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"\\uD83D\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary,\n                                                flex: 1\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#EF4444\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"11px\",\n                                                color: \"white\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/support\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"❓\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary\n                                            },\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"12px 8px\",\n                                    marginTop: \"8px\",\n                                    cursor: \"pointer\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: colors.primary,\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"white\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.primary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Rahat Ali\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.text.tertiary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Super Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.background,\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 619,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/dashboard.tsx":
/*!*****************************!*\
  !*** ./pages/dashboard.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/dashboard.tsx\n\n\n\nconst DashboardPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"var(--accent-color)\",\n                    marginBottom: \"24px\"\n                },\n                children: \"Dashboard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n                    gap: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Quick Stats\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Meetings: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"150\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 62\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Tweets Sent: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 65\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Average Meeting Duration: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"30 mins\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 40\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Meeting with John Doe ended.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Tweet about new feature sent.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Scheduled meeting with Jane Smith.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\nDashboardPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();