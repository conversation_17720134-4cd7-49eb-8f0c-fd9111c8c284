/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard";
exports.ids = ["pages/dashboard"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/dashboard.tsx */ \"./pages/dashboard.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SidebarLayout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    backgroundColor: \"#f0f0f0\",\n                    padding: \"16px\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            listStyle: \"none\",\n                            padding: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/tweet-center\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Tweet Center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/meeting\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"AI Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    padding: \"24px\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/dashboard.tsx":
/*!*****************************!*\
  !*** ./pages/dashboard.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/dashboard.tsx\n\n\n\nconst DashboardPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"24px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"var(--accent-color)\",\n                    marginBottom: \"24px\"\n                },\n                children: \"Dashboard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n                    gap: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Quick Stats\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Meetings: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"150\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 62\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginBottom: \"8px\"\n                                },\n                                children: [\n                                    \"Total Tweets Sent: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 65\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"Average Meeting Duration: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            color: \"var(--accent-color)\"\n                                        },\n                                        children: \"30 mins\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 40\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#fff\",\n                            padding: \"24px\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n                            border: \"1px solid #eee\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"#333\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Recent Activity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Meeting with John Doe ended.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            marginBottom: \"12px\",\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Tweet about new feature sent.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        style: {\n                                            paddingBottom: \"12px\",\n                                            borderBottom: \"1px solid #eee\"\n                                        },\n                                        children: \"Scheduled meeting with Jane Smith.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\nDashboardPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/dashboard.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fpages%2Fdashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();