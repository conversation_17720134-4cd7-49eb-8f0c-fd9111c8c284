/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2hldnJvbkxlZnQsQ2hldnJvblJpZ2h0LEhvbWUsTWVzc2FnZUNpcmNsZSxWaWRlbyE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNHO0FBQ0U7QUFDaEI7QUFDa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/MjYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uTGVmdCB9IGZyb20gXCIuL2ljb25zL2NoZXZyb24tbGVmdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25SaWdodCB9IGZyb20gXCIuL2ljb25zL2NoZXZyb24tcmlnaHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lIH0gZnJvbSBcIi4vaWNvbnMvaG91c2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBWaWRlbyB9IGZyb20gXCIuL2ljb25zL3ZpZGVvLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: `\n        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),\n        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),\n        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)\n      `,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"16px\",\n                    boxShadow: `\n          0 20px 60px ${colors.sidebar.glow},\n          0 8px 32px rgba(0, 0, 0, 0.15),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `,\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: `1px solid rgba(255, 255, 255, 0.1)`\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"20px 8px\" : \"24px 16px\",\n                            borderBottom: `1px solid ${colors.sidebar.border}`,\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            position: \"relative\",\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: isCollapsed ? \"28px\" : \"36px\",\n                                    color: colors.sidebar.text,\n                                    fontWeight: \"400\",\n                                    fontFamily: \"Georgia, serif\",\n                                    fontStyle: \"italic\",\n                                    textShadow: \"0 4px 12px rgba(0, 0, 0, 0.3)\",\n                                    letterSpacing: \"-2px\",\n                                    transition: \"font-size 0.3s ease\"\n                                },\n                                children: \"ℰ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                style: {\n                                    position: \"absolute\",\n                                    right: isCollapsed ? \"6px\" : \"8px\",\n                                    top: \"50%\",\n                                    transform: \"translateY(-50%)\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    borderRadius: \"6px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)`,\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    cursor: \"pointer\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    transition: \"all 0.3s ease\",\n                                    backdropFilter: \"blur(10px)\"\n                                },\n                                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                                    size: 12,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                    size: 12,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"16px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: isCollapsed ? \"0 6px\" : \"0 12px\",\n                                transition: \"padding 0.3s ease\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\",\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            onMouseEnter: ()=>setHoveredItem(item.href),\n                                            onMouseLeave: ()=>setHoveredItem(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    padding: isCollapsed ? \"10px 6px\" : \"10px 12px\",\n                                                    borderRadius: \"12px\",\n                                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                    cursor: \"pointer\",\n                                                    position: \"relative\",\n                                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\",\n                                                    background: active ? `\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\n                        ` : hovered ? `\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\n                          ` : \"transparent\",\n                                                    backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                    border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                    boxShadow: active ? `\n                          0 8px 32px rgba(0, 0, 0, 0.1),\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\n                        ` : hovered ? `\n                            0 4px 16px rgba(0, 0, 0, 0.05),\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\n                          ` : \"none\",\n                                                    transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                                },\n                                                children: [\n                                                    active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            left: \"-2px\",\n                                                            top: \"50%\",\n                                                            transform: \"translateY(-50%)\",\n                                                            width: \"4px\",\n                                                            height: \"24px\",\n                                                            background: `\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\n                          `,\n                                                            borderRadius: \"0 8px 8px 0\",\n                                                            boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"28px\",\n                                                            height: \"28px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            marginRight: isCollapsed ? \"0\" : \"10px\",\n                                                            borderRadius: \"8px\",\n                                                            background: active ? `\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\n                          ` : hovered ? `\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n                            ` : \"transparent\",\n                                                            transition: \"all 0.3s ease\",\n                                                            transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 16,\n                                                            color: colors.sidebar.text,\n                                                            style: {\n                                                                transition: \"all 0.3s ease\",\n                                                                filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            fontWeight: active ? \"600\" : \"500\",\n                                                            color: colors.sidebar.text,\n                                                            transition: \"all 0.3s ease\",\n                                                            letterSpacing: \"-0.2px\",\n                                                            textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\",\n                                                            opacity: isCollapsed ? 0 : 1,\n                                                            transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                                        },\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    hovered && !active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            right: \"12px\",\n                                                            width: \"6px\",\n                                                            height: \"6px\",\n                                                            borderRadius: \"50%\",\n                                                            background: `\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\n                          `,\n                                                            boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isCollapsed && hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"55px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: `linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)`,\n                                                color: \"white\",\n                                                padding: \"8px 12px\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                whiteSpace: \"nowrap\",\n                                                zIndex: 1000,\n                                                boxShadow: \"0 8px 24px rgba(0, 0, 0, 0.3)\",\n                                                backdropFilter: \"blur(10px)\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                                pointerEvents: \"none\"\n                                            },\n                                            children: [\n                                                item.label,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-4px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: 0,\n                                                        height: 0,\n                                                        borderTop: \"4px solid transparent\",\n                                                        borderBottom: \"4px solid transparent\",\n                                                        borderRight: \"4px solid rgba(0, 0, 0, 0.9)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.href, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"16px 6px\" : \"16px 12px\",\n                            borderTop: `1px solid ${colors.sidebar.border}`,\n                            marginTop: \"auto\",\n                            background: `\n            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\n          `,\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: isCollapsed ? \"0\" : \"10px\",\n                                padding: isCollapsed ? \"10px 6px\" : \"10px 12px\",\n                                background: `\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n            `,\n                                borderRadius: \"16px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                boxShadow: `\n              0 8px 32px rgba(0, 0, 0, 0.1),\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\n            `,\n                                justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"36px\",\n                                        height: \"36px\",\n                                        background: `\n                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\n              `,\n                                        borderRadius: \"10px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        backdropFilter: \"blur(10px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: `\n                0 4px 16px rgba(0, 0, 0, 0.1),\n                inset 0 1px 0 rgba(255, 255, 255, 0.4)\n              `\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-2px\",\n                                                right: \"-2px\",\n                                                width: \"10px\",\n                                                height: \"10px\",\n                                                borderRadius: \"50%\",\n                                                background: `\n                  radial-gradient(circle, #00E676 0%, #00C853 100%)\n                `,\n                                                border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                flex: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.sidebar.text,\n                                                        lineHeight: \"1.2\",\n                                                        marginBottom: \"2px\",\n                                                        textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"Alex Chen\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.sidebar.textTertiary,\n                                                        lineHeight: \"1.2\",\n                                                        fontWeight: \"500\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"AI Manager\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"20px\",\n                                                height: \"20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                borderRadius: \"6px\",\n                                                background: `\n                    linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n                  `,\n                                                transition: \"all 0.3s ease\",\n                                                opacity: isCollapsed ? 0 : 1,\n                                                transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.sidebar.textSecondary,\n                                                    transform: \"rotate(0deg)\",\n                                                    transition: \"transform 0.3s ease\"\n                                                },\n                                                children: \"⌄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: `\n            0 32px 80px rgba(0, 0, 0, 0.12),\n            0 8px 32px rgba(0, 0, 0, 0.08),\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\n            0 0 0 1px rgba(255, 107, 53, 0.1)\n          `,\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: `\n              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)\n            `,\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\",\n                        marginBottom: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"48px\",\n                                height: \"48px\",\n                                borderRadius: \"16px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: `0 8px 24px ${colors.primary}30`\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: \"24px\"\n                                },\n                                children: \"\\uD83C\\uDFE0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"32px\",\n                                        fontWeight: \"700\",\n                                        letterSpacing: \"-1px\"\n                                    },\n                                    children: \"Briefing Room\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        margin: 0\n                                    },\n                                    children: \"Your daily mission control center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B220 100%)`,\n                    borderRadius: \"24px\",\n                    padding: \"32px\",\n                    marginBottom: \"32px\",\n                    boxShadow: `\n          0 20px 60px rgba(0, 0, 0, 0.08),\n          0 8px 32px rgba(0, 0, 0, 0.04),\n          inset 0 1px 0 rgba(255, 255, 255, 0.9)\n        `,\n                    border: `1px solid ${colors.border}`,\n                    position: \"relative\",\n                    overflow: \"hidden\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"100px\",\n                            background: `radial-gradient(ellipse at top, #FFE0B230 0%, transparent 70%)`,\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            borderRadius: \"10px\",\n                                            background: `linear-gradient(135deg, ${colors.primary}20 0%, #FF8A6520 100%)`,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Today's Mission\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"4px 12px\",\n                                            background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                            borderRadius: \"12px\",\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Generated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.text.secondary,\n                                    fontSize: \"18px\",\n                                    lineHeight: \"1.6\",\n                                    margin: 0,\n                                    marginBottom: \"24px\"\n                                },\n                                children: '\"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(3, 1fr)\",\n                                    gap: \"20px\",\n                                    marginBottom: \"24px\"\n                                },\n                                children: [\n                                    {\n                                        label: \"Engagement Rate\",\n                                        value: \"+24%\",\n                                        icon: \"\\uD83D\\uDCC8\"\n                                    },\n                                    {\n                                        label: \"New Followers\",\n                                        value: \"127\",\n                                        icon: \"\\uD83D\\uDC65\"\n                                    },\n                                    {\n                                        label: \"Content Score\",\n                                        value: \"8.9/10\",\n                                        icon: \"⭐\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)`,\n                                            borderRadius: \"16px\",\n                                            padding: \"20px\",\n                                            textAlign: \"center\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"700\",\n                                                    color: colors.text.primary,\n                                                    marginBottom: \"4px\"\n                                                },\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"16px\",\n                                    flexWrap: \"wrap\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/meeting\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                boxShadow: `0 8px 24px ${colors.primary}40`,\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"\\uD83C\\uDFA5 Join Call\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"16px 24px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                            color: colors.text.primary,\n                                            border: `1px solid ${colors.border}`,\n                                            borderRadius: \"16px\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            backdropFilter: \"blur(10px)\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        children: \"\\uD83E\\uDD16 Ask Mentor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/tweet-center\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                                color: colors.text.primary,\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                backdropFilter: \"blur(10px)\",\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"✍️ Generate Tweet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B215 100%)`,\n                    borderRadius: \"20px\",\n                    padding: \"24px\",\n                    boxShadow: `\n          0 12px 40px rgba(0, 0, 0, 0.06),\n          inset 0 1px 0 rgba(255, 255, 255, 0.8)\n        `,\n                    border: `1px solid ${colors.border}`\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"64px\",\n                                height: \"64px\",\n                                borderRadius: \"20px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: `0 8px 24px ${colors.primary}30`,\n                                position: \"relative\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"32px\"\n                                    },\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"absolute\",\n                                        bottom: \"-2px\",\n                                        right: \"-2px\",\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        borderRadius: \"50%\",\n                                        background: \"#00E676\",\n                                        border: \"2px solid white\",\n                                        boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"18px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"4px\"\n                                    },\n                                    children: \"AI Mentor\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: '\"Ready to help you create content that resonates. What\\'s on your mind today?\"'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();