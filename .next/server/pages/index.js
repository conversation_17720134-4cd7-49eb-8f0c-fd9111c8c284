/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsSG9tZSxNZXNzYWdlQ2lyY2xlLFZpZGVvIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDWDtBQUNrQiIsInNvdXJjZXMiOlsid2VicGFjazovL2V4aWUtYWkvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz8zODc4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJDaGFydDMgfSBmcm9tIFwiLi9pY29ucy9iYXItY2hhcnQtMy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWUgfSBmcm9tIFwiLi9pY29ucy9ob3VzZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lc3NhZ2VDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9tZXNzYWdlLWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFZpZGVvIH0gZnJvbSBcIi4vaWNvbnMvdmlkZW8uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlMkZpbmRleC50c3gmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUNoQztBQUNMO0FBQzFEO0FBQ29EO0FBQ1Y7QUFDMUM7QUFDOEM7QUFDOUM7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLDZDQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLHVCQUF1Qix3RUFBSyxDQUFDLDZDQUFRO0FBQ3JDLHVCQUF1Qix3RUFBSyxDQUFDLDZDQUFRO0FBQ3JDLDJCQUEyQix3RUFBSyxDQUFDLDZDQUFRO0FBQ3pDLGVBQWUsd0VBQUssQ0FBQyw2Q0FBUTtBQUM3Qix3QkFBd0Isd0VBQUssQ0FBQyw2Q0FBUTtBQUM3QztBQUNPLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLGlDQUFpQyx3RUFBSyxDQUFDLDZDQUFRO0FBQy9DLGdDQUFnQyx3RUFBSyxDQUFDLDZDQUFRO0FBQzlDLG9DQUFvQyx3RUFBSyxDQUFDLDZDQUFRO0FBQ3pEO0FBQ08sd0JBQXdCLHlHQUFnQjtBQUMvQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVc7QUFDWCxnQkFBZ0I7QUFDaEIsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8/MWMyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIGFwcCBhbmQgZG9jdW1lbnQgbW9kdWxlcy5cbmltcG9ydCBEb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0IEFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2luZGV4LnRzeFwiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsIFwiZGVmYXVsdFwiKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsIFwiZ2V0U3RhdGljUHJvcHNcIik7XG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUGF0aHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTdGF0aWNQYXRoc1wiKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTZXJ2ZXJTaWRlUHJvcHNcIik7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsIFwiY29uZmlnXCIpO1xuZXhwb3J0IGNvbnN0IHJlcG9ydFdlYlZpdGFscyA9IGhvaXN0KHVzZXJsYW5kLCBcInJlcG9ydFdlYlZpdGFsc1wiKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFN0YXRpY1Byb3BzXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGF0aHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyUHJvcHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzXCIpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVMsXG4gICAgICAgIHBhZ2U6IFwiL2luZGV4XCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgY29tcG9uZW50czoge1xuICAgICAgICBBcHAsXG4gICAgICAgIERvY3VtZW50XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const accountMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (accountMenuRef.current && !accountMenuRef.current.contains(event.target)) {\n                setShowAccountMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: `\n        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),\n        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),\n        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)\n      `,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"16px\",\n                    boxShadow: `\n          0 20px 60px ${colors.sidebar.glow},\n          0 8px 32px rgba(0, 0, 0, 0.15),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `,\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: `1px solid rgba(255, 255, 255, 0.1)`\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px\",\n                            borderBottom: `1px solid ${colors.sidebar.border}`,\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"32px\",\n                                color: colors.sidebar.text,\n                                fontWeight: \"400\",\n                                fontFamily: \"Georgia, serif\",\n                                fontStyle: \"italic\",\n                                textShadow: \"0 2px 8px rgba(0, 0, 0, 0.3)\",\n                                letterSpacing: \"-1px\"\n                            },\n                            children: \"ℰ\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: \"0 16px\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"6px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        onMouseEnter: ()=>setHoveredItem(item.href),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"12px 16px\",\n                                                borderRadius: \"12px\",\n                                                transition: \"all 0.2s ease\",\n                                                cursor: \"pointer\",\n                                                position: \"relative\",\n                                                background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)` : hovered ? `linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)` : \"transparent\",\n                                                backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                boxShadow: active ? `0 4px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)` : \"none\"\n                                            },\n                                            children: [\n                                                active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-1px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: \"3px\",\n                                                        height: \"20px\",\n                                                        background: `linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                                        borderRadius: \"0 6px 6px 0\",\n                                                        boxShadow: \"0 0 8px rgba(255, 255, 255, 0.5)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"24px\",\n                                                        height: \"24px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        marginRight: \"12px\",\n                                                        borderRadius: \"6px\",\n                                                        background: active ? `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)` : \"transparent\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        size: 16,\n                                                        color: colors.sidebar.text,\n                                                        style: {\n                                                            filter: active ? \"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"15px\",\n                                                        fontWeight: active ? \"600\" : \"500\",\n                                                        color: colors.sidebar.text,\n                                                        letterSpacing: \"-0.3px\",\n                                                        textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\"\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: accountMenuRef,\n                        style: {\n                            padding: \"16px 12px\",\n                            borderTop: `1px solid ${colors.sidebar.border}`,\n                            marginTop: \"auto\",\n                            background: `radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)`,\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setShowAccountMenu(!showAccountMenu),\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\",\n                                    padding: \"8px 12px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                    borderRadius: \"8px\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\",\n                                    backdropFilter: \"blur(10px)\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: `0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.3)`\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"24px\",\n                                            height: \"24px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)`,\n                                            borderRadius: \"6px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            position: \"relative\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                            flexShrink: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: colors.sidebar.text,\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                                },\n                                                children: \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"-1px\",\n                                                    right: \"-1px\",\n                                                    width: \"6px\",\n                                                    height: \"6px\",\n                                                    borderRadius: \"50%\",\n                                                    background: `radial-gradient(circle, #00E676 0%, #00C853 100%)`,\n                                                    border: \"1px solid rgba(255, 255, 255, 0.9)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            minWidth: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.sidebar.text,\n                                                    lineHeight: \"1.2\",\n                                                    marginBottom: \"1px\",\n                                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                    overflow: \"hidden\",\n                                                    textOverflow: \"ellipsis\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: \"Alex Chen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"10px\",\n                                                    color: colors.sidebar.textTertiary,\n                                                    lineHeight: \"1.2\",\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: \"AI Manager\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"12px\",\n                                            height: \"12px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            borderRadius: \"3px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)`,\n                                            flexShrink: 0\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"8px\",\n                                                color: colors.sidebar.textSecondary,\n                                                transform: showAccountMenu ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                transition: \"transform 0.2s ease\"\n                                            },\n                                            children: \"⌄\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined),\n                            showAccountMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    bottom: \"100%\",\n                                    left: \"8px\",\n                                    right: \"8px\",\n                                    marginBottom: \"12px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%)`,\n                                    borderRadius: \"16px\",\n                                    boxShadow: `\n                0 20px 60px rgba(0, 0, 0, 0.15),\n                0 8px 32px rgba(0, 0, 0, 0.1),\n                inset 0 1px 0 rgba(255, 255, 255, 0.8)\n              `,\n                                    border: `1px solid rgba(255, 255, 255, 0.3)`,\n                                    overflow: \"hidden\",\n                                    zIndex: 1000,\n                                    backdropFilter: \"blur(20px)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"absolute\",\n                                            top: 0,\n                                            left: 0,\n                                            right: 0,\n                                            height: \"40px\",\n                                            background: `radial-gradient(ellipse at top, ${colors.sidebar.glow}30 0%, transparent 70%)`,\n                                            pointerEvents: \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    [\n                                        {\n                                            label: \"Account Settings\",\n                                            desc: \"Profile & preferences\"\n                                        },\n                                        {\n                                            label: \"Subscription\",\n                                            desc: \"Manage your plan\"\n                                        },\n                                        {\n                                            label: \"Billing\",\n                                            desc: \"Payment & invoices\"\n                                        },\n                                        {\n                                            label: \"Help & Support\",\n                                            desc: \"Get assistance\"\n                                        },\n                                        {\n                                            label: \"Sign Out\",\n                                            desc: \"End your session\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"14px 16px\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                borderBottom: index < 4 ? `1px solid rgba(255, 255, 255, 0.3)` : \"none\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: \"12px\",\n                                                position: \"relative\",\n                                                background: \"transparent\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.background = `linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 100%)`;\n                                                e.currentTarget.style.transform = \"translateX(2px)\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.background = \"transparent\";\n                                                e.currentTarget.style.transform = \"translateX(0)\";\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"28px\",\n                                                        height: \"28px\",\n                                                        borderRadius: \"8px\",\n                                                        background: `linear-gradient(135deg, ${colors.sidebar.glow}20 0%, ${colors.sidebar.glow}10 100%)`,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        border: `1px solid ${colors.sidebar.glow}30`,\n                                                        boxShadow: `0 2px 8px ${colors.sidebar.glow}20`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"12px\",\n                                                            height: \"12px\",\n                                                            borderRadius: \"3px\",\n                                                            background: `linear-gradient(135deg, ${colors.sidebar.glow} 0%, #FF8A65 100%)`,\n                                                            boxShadow: `0 1px 3px rgba(0, 0, 0, 0.2)`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        flex: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"13px\",\n                                                                fontWeight: \"600\",\n                                                                color: colors.sidebar.text,\n                                                                marginBottom: \"2px\",\n                                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                                            },\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"11px\",\n                                                                color: colors.sidebar.textTertiary,\n                                                                fontWeight: \"500\"\n                                                            },\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\",\n                                                        borderRadius: \"4px\",\n                                                        background: `linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%)`,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        opacity: 0.6\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: \"8px\",\n                                                            color: colors.sidebar.textSecondary,\n                                                            transform: \"rotate(-90deg)\"\n                                                        },\n                                                        children: \"⌄\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: `\n            0 32px 80px rgba(0, 0, 0, 0.12),\n            0 8px 32px rgba(0, 0, 0, 0.08),\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\n            0 0 0 1px rgba(255, 107, 53, 0.1)\n          `,\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: `\n              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)\n            `,\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"32px\",\n            height: \"100vh\",\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"32px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            color: colors.text.primary,\n                            margin: 0,\n                            fontSize: \"28px\",\n                            fontWeight: \"600\",\n                            letterSpacing: \"-0.5px\",\n                            marginBottom: \"8px\"\n                        },\n                        children: \"Briefing Room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: colors.text.secondary,\n                            fontSize: \"16px\",\n                            margin: 0,\n                            fontWeight: \"400\"\n                        },\n                        children: \"Your daily mission control center\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"800px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            marginBottom: \"24px\",\n                            boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"space-between\",\n                                        marginBottom: \"12px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"20px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"Today's Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"4px 8px\",\n                                                background: `${colors.primary}15`,\n                                                borderRadius: \"6px\",\n                                                color: colors.primary,\n                                                fontSize: \"11px\",\n                                                fontWeight: \"600\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.5px\"\n                                            },\n                                            children: \"AI Generated\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        lineHeight: \"1.5\",\n                                        margin: 0,\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: \"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(3, 1fr)\",\n                                        gap: \"16px\",\n                                        marginBottom: \"20px\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"Engagement Rate\",\n                                            value: \"+24%\"\n                                        },\n                                        {\n                                            label: \"New Followers\",\n                                            value: \"127\"\n                                        },\n                                        {\n                                            label: \"Content Score\",\n                                            value: \"8.9/10\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: `${colors.primary}08`,\n                                                borderRadius: \"12px\",\n                                                padding: \"16px\",\n                                                textAlign: \"center\",\n                                                border: `1px solid ${colors.primary}15`\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"20px\",\n                                                        fontWeight: \"700\",\n                                                        color: colors.text.primary,\n                                                        marginBottom: \"4px\"\n                                                    },\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"13px\",\n                                                        color: colors.text.tertiary,\n                                                        fontWeight: \"500\"\n                                                    },\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"12px\",\n                                        flexWrap: \"wrap\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/meeting\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.primary,\n                                                    color: \"white\",\n                                                    border: \"none\",\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Join Call\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"12px 20px\",\n                                                background: colors.surface,\n                                                color: colors.text.primary,\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                transition: \"all 0.2s ease\"\n                                            },\n                                            children: \"Ask Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/tweet-center\",\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                style: {\n                                                    padding: \"12px 20px\",\n                                                    background: colors.surface,\n                                                    color: colors.text.primary,\n                                                    border: `1px solid ${colors.border}`,\n                                                    borderRadius: \"8px\",\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    cursor: \"pointer\",\n                                                    transition: \"all 0.2s ease\"\n                                                },\n                                                children: \"Create Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: colors.surface,\n                            borderRadius: \"16px\",\n                            padding: \"24px\",\n                            boxShadow: `0 4px 16px rgba(0, 0, 0, 0.04)`,\n                            border: `1px solid ${colors.border}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"flex-start\",\n                                gap: \"16px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"48px\",\n                                        height: \"48px\",\n                                        borderRadius: \"12px\",\n                                        background: `${colors.primary}15`,\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        flexShrink: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                borderRadius: \"6px\",\n                                                background: colors.primary\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"2px\",\n                                                right: \"2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#00E676\",\n                                                border: \"2px solid white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: colors.text.primary,\n                                                margin: 0,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                marginBottom: \"8px\"\n                                            },\n                                            children: \"AI Mentor\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: colors.text.secondary,\n                                                margin: 0,\n                                                fontSize: \"15px\",\n                                                lineHeight: \"1.5\"\n                                            },\n                                            children: \"Ready to help you create content that resonates. What's on your mind today?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();