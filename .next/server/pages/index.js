/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Home: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MessageCircle: () => (/* reexport safe */ _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Video: () => (/* reexport safe */ _icons_video_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/house.js */ \"./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _icons_message_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/message-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _icons_video_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/video.js */ \"./node_modules/lucide-react/dist/esm/icons/video.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2hldnJvbkxlZnQsQ2hldnJvblJpZ2h0LEhvbWUsTWVzc2FnZUNpcmNsZSxWaWRlbyE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNHO0FBQ0U7QUFDaEI7QUFDa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/MjYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQmFyQ2hhcnQzIH0gZnJvbSBcIi4vaWNvbnMvYmFyLWNoYXJ0LTMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uTGVmdCB9IGZyb20gXCIuL2ljb25zL2NoZXZyb24tbGVmdC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25SaWdodCB9IGZyb20gXCIuL2ljb25zL2NoZXZyb24tcmlnaHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIb21lIH0gZnJvbSBcIi4vaWNvbnMvaG91c2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvbWVzc2FnZS1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBWaWRlbyB9IGZyb20gXCIuL2ljb25zL3ZpZGVvLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,Home,MessageCircle,Video!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Warm Intelligence Theme - Focused, Intelligent, Warm\n    const colors = {\n        primary: \"#FF6B35\",\n        primaryLight: \"#FF8A65\",\n        primaryDark: \"#E65100\",\n        accent: \"#FFF3E0\",\n        surface: \"#FEFEFE\",\n        surfaceElevated: \"#FFFFFF\",\n        background: \"#FFF8F3\",\n        warmGlow: \"#FFE0B2\",\n        text: {\n            primary: \"#2D1B14\",\n            secondary: \"#5D4037\",\n            tertiary: \"#8D6E63\",\n            muted: \"#BCAAA4\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#F5E6D3\",\n            medium: \"#E1C4A0\",\n            primary: \"#FF6B35\" // Primary border\n        },\n        sidebar: {\n            background: \"linear-gradient(135deg, #FF6B35 0%, #FF8A65 50%, #FFB74D 100%)\",\n            backgroundSolid: \"#FF6B35\",\n            text: \"#FFFFFF\",\n            textSecondary: \"rgba(255, 255, 255, 0.9)\",\n            textTertiary: \"rgba(255, 255, 255, 0.7)\",\n            hover: \"rgba(255, 255, 255, 0.15)\",\n            active: \"rgba(255, 255, 255, 0.25)\",\n            border: \"rgba(255, 255, 255, 0.2)\",\n            glow: \"rgba(255, 107, 53, 0.3)\"\n        }\n    };\n    const menuItems = [\n        {\n            href: \"/\",\n            label: \"Briefing Room\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Home,\n            description: \"Mission Control\"\n        },\n        {\n            href: \"/tweet-center\",\n            label: \"Drafting Desk\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.MessageCircle,\n            description: \"AI Writing\"\n        },\n        {\n            href: \"/dashboard\",\n            label: \"Growth Lab\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.BarChart3,\n            description: \"Analytics\"\n        },\n        {\n            href: \"/meeting\",\n            label: \"AI Meetings\",\n            icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Video,\n            description: \"Video Calls\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return router.pathname === \"/\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            background: `\n        radial-gradient(circle at 20% 20%, ${colors.primary}15 0%, transparent 50%),\n        radial-gradient(circle at 80% 80%, ${colors.primaryLight}10 0%, transparent 50%),\n        linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)\n      `,\n            padding: \"20px\",\n            gap: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: isCollapsed ? \"80px\" : \"240px\",\n                    background: colors.sidebar.background,\n                    minHeight: \"calc(100vh - 40px)\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    borderRadius: \"20px\",\n                    boxShadow: `\n          0 20px 60px ${colors.sidebar.glow},\n          0 8px 32px rgba(0, 0, 0, 0.15),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        `,\n                    overflow: \"hidden\",\n                    backdropFilter: \"blur(10px)\",\n                    border: `1px solid rgba(255, 255, 255, 0.1)`,\n                    transition: \"width 0.3s cubic-bezier(0.4, 0, 0.2, 1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"24px 12px\" : \"32px 24px\",\n                            borderBottom: `1px solid ${colors.sidebar.border}`,\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            position: \"relative\",\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: isCollapsed ? \"32px\" : \"48px\",\n                                    color: colors.sidebar.text,\n                                    fontWeight: \"400\",\n                                    fontFamily: \"Georgia, serif\",\n                                    fontStyle: \"italic\",\n                                    textShadow: \"0 4px 12px rgba(0, 0, 0, 0.3)\",\n                                    letterSpacing: \"-2px\",\n                                    transition: \"font-size 0.3s ease\"\n                                },\n                                children: \"ℰ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                style: {\n                                    position: \"absolute\",\n                                    right: isCollapsed ? \"8px\" : \"12px\",\n                                    top: \"50%\",\n                                    transform: \"translateY(-50%)\",\n                                    width: \"24px\",\n                                    height: \"24px\",\n                                    borderRadius: \"6px\",\n                                    background: `linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)`,\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    cursor: \"pointer\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    transition: \"all 0.3s ease\",\n                                    backdropFilter: \"blur(10px)\"\n                                },\n                                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                                    size: 14,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_Home_MessageCircle_Video_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronLeft, {\n                                    size: 14,\n                                    color: colors.sidebar.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"20px 0\",\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: isCollapsed ? \"0 8px\" : \"0 16px\",\n                                transition: \"padding 0.3s ease\"\n                            },\n                            children: menuItems.map((item, index)=>{\n                                const active = isActive(item.href);\n                                const hovered = hoveredItem === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"8px\",\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            style: {\n                                                textDecoration: \"none\"\n                                            },\n                                            onMouseEnter: ()=>setHoveredItem(item.href),\n                                            onMouseLeave: ()=>setHoveredItem(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    padding: isCollapsed ? \"12px 8px\" : \"12px 16px\",\n                                                    borderRadius: \"16px\",\n                                                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                                    cursor: \"pointer\",\n                                                    position: \"relative\",\n                                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\",\n                                                    background: active ? `\n                          linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)\n                        ` : hovered ? `\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)\n                          ` : \"transparent\",\n                                                    backdropFilter: active || hovered ? \"blur(10px)\" : \"none\",\n                                                    border: active ? \"1px solid rgba(255, 255, 255, 0.3)\" : \"1px solid transparent\",\n                                                    boxShadow: active ? `\n                          0 8px 32px rgba(0, 0, 0, 0.1),\n                          inset 0 1px 0 rgba(255, 255, 255, 0.4)\n                        ` : hovered ? `\n                            0 4px 16px rgba(0, 0, 0, 0.05),\n                            inset 0 1px 0 rgba(255, 255, 255, 0.2)\n                          ` : \"none\",\n                                                    transform: hovered ? \"translateY(-1px) scale(1.02)\" : \"translateY(0) scale(1)\"\n                                                },\n                                                children: [\n                                                    active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            left: \"-2px\",\n                                                            top: \"50%\",\n                                                            transform: \"translateY(-50%)\",\n                                                            width: \"4px\",\n                                                            height: \"24px\",\n                                                            background: `\n                            linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)\n                          `,\n                                                            borderRadius: \"0 8px 8px 0\",\n                                                            boxShadow: \"0 0 12px rgba(255, 255, 255, 0.5)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: \"32px\",\n                                                            height: \"32px\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            marginRight: isCollapsed ? \"0\" : \"12px\",\n                                                            borderRadius: \"10px\",\n                                                            background: active ? `\n                            linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\n                          ` : hovered ? `\n                              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n                            ` : \"transparent\",\n                                                            transition: \"all 0.3s ease\",\n                                                            transform: hovered ? \"scale(1.1)\" : \"scale(1)\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 18,\n                                                            color: colors.sidebar.text,\n                                                            style: {\n                                                                transition: \"all 0.3s ease\",\n                                                                filter: active ? \"drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))\" : \"none\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"15px\",\n                                                            fontWeight: active ? \"600\" : \"500\",\n                                                            color: colors.sidebar.text,\n                                                            transition: \"all 0.3s ease\",\n                                                            letterSpacing: \"-0.2px\",\n                                                            textShadow: active ? \"0 1px 2px rgba(0, 0, 0, 0.1)\" : \"none\",\n                                                            opacity: isCollapsed ? 0 : 1,\n                                                            transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                                        },\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    hovered && !active && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            right: \"12px\",\n                                                            width: \"6px\",\n                                                            height: \"6px\",\n                                                            borderRadius: \"50%\",\n                                                            background: `\n                            radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%)\n                          `,\n                                                            boxShadow: \"0 0 8px rgba(255, 255, 255, 0.6)\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isCollapsed && hovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                left: \"70px\",\n                                                top: \"50%\",\n                                                transform: \"translateY(-50%)\",\n                                                background: `linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)`,\n                                                color: \"white\",\n                                                padding: \"8px 12px\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                whiteSpace: \"nowrap\",\n                                                zIndex: 1000,\n                                                boxShadow: \"0 8px 24px rgba(0, 0, 0, 0.3)\",\n                                                backdropFilter: \"blur(10px)\",\n                                                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                                pointerEvents: \"none\"\n                                            },\n                                            children: [\n                                                item.label,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        left: \"-4px\",\n                                                        top: \"50%\",\n                                                        transform: \"translateY(-50%)\",\n                                                        width: 0,\n                                                        height: 0,\n                                                        borderTop: \"4px solid transparent\",\n                                                        borderBottom: \"4px solid transparent\",\n                                                        borderRight: \"4px solid rgba(0, 0, 0, 0.9)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, item.href, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: isCollapsed ? \"20px 8px\" : \"20px 16px\",\n                            borderTop: `1px solid ${colors.sidebar.border}`,\n                            marginTop: \"auto\",\n                            background: `\n            radial-gradient(circle at center bottom, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\n          `,\n                            transition: \"padding 0.3s ease\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: isCollapsed ? \"0\" : \"12px\",\n                                padding: isCollapsed ? \"12px 8px\" : \"12px 16px\",\n                                background: `\n              linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n            `,\n                                borderRadius: \"16px\",\n                                cursor: \"pointer\",\n                                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                                backdropFilter: \"blur(10px)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                boxShadow: `\n              0 8px 32px rgba(0, 0, 0, 0.1),\n              inset 0 1px 0 rgba(255, 255, 255, 0.3)\n            `,\n                                justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        background: `\n                linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%)\n              `,\n                                        borderRadius: \"12px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        position: \"relative\",\n                                        backdropFilter: \"blur(10px)\",\n                                        border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                        boxShadow: `\n                0 4px 16px rgba(0, 0, 0, 0.1),\n                inset 0 1px 0 rgba(255, 255, 255, 0.4)\n              `\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: colors.sidebar.text,\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                            },\n                                            children: \"A\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"absolute\",\n                                                bottom: \"-2px\",\n                                                right: \"-2px\",\n                                                width: \"12px\",\n                                                height: \"12px\",\n                                                borderRadius: \"50%\",\n                                                background: `\n                  radial-gradient(circle, #00E676 0%, #00C853 100%)\n                `,\n                                                border: \"2px solid rgba(255, 255, 255, 0.9)\",\n                                                boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                flex: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.sidebar.text,\n                                                        lineHeight: \"1.2\",\n                                                        marginBottom: \"2px\",\n                                                        textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"Alex Chen\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.sidebar.textTertiary,\n                                                        lineHeight: \"1.2\",\n                                                        fontWeight: \"500\",\n                                                        opacity: isCollapsed ? 0 : 1,\n                                                        transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: \"AI Manager\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"20px\",\n                                                height: \"20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                borderRadius: \"6px\",\n                                                background: `\n                    linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)\n                  `,\n                                                transition: \"all 0.3s ease\",\n                                                opacity: isCollapsed ? 0 : 1,\n                                                transform: isCollapsed ? \"translateX(-10px)\" : \"translateX(0)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.sidebar.textSecondary,\n                                                    transform: \"rotate(0deg)\",\n                                                    transition: \"transform 0.3s ease\"\n                                                },\n                                                children: \"⌄\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    position: \"relative\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: colors.surfaceElevated,\n                        borderRadius: \"20px\",\n                        minHeight: \"calc(100vh - 40px)\",\n                        boxShadow: `\n            0 32px 80px rgba(0, 0, 0, 0.12),\n            0 8px 32px rgba(0, 0, 0, 0.08),\n            inset 0 1px 0 rgba(255, 255, 255, 0.9),\n            0 0 0 1px rgba(255, 107, 53, 0.1)\n          `,\n                        overflow: \"hidden\",\n                        position: \"relative\",\n                        backdropFilter: \"blur(20px)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"absolute\",\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                height: \"200px\",\n                                background: `\n              radial-gradient(ellipse at top, ${colors.warmGlow}20 0%, transparent 70%)\n            `,\n                                pointerEvents: \"none\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                position: \"relative\",\n                                zIndex: 1,\n                                height: \"100%\"\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n// pages/index.tsx\n\n\n\n\nconst HomePage = ()=>{\n    const colors = {\n        primary: \"#F97316\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\"\n        },\n        border: \"#E5E7EB\",\n        surface: \"#FFFFFF\",\n        background: \"#F8F9FA\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"40px\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"40px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\",\n                        marginBottom: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"48px\",\n                                height: \"48px\",\n                                borderRadius: \"16px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: `0 8px 24px ${colors.primary}30`\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: \"24px\"\n                                },\n                                children: \"\\uD83C\\uDFE0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"32px\",\n                                        fontWeight: \"700\",\n                                        letterSpacing: \"-1px\"\n                                    },\n                                    children: \"Briefing Room\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        fontSize: \"16px\",\n                                        margin: 0\n                                    },\n                                    children: \"Your daily mission control center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B220 100%)`,\n                    borderRadius: \"24px\",\n                    padding: \"32px\",\n                    marginBottom: \"32px\",\n                    boxShadow: `\n          0 20px 60px rgba(0, 0, 0, 0.08),\n          0 8px 32px rgba(0, 0, 0, 0.04),\n          inset 0 1px 0 rgba(255, 255, 255, 0.9)\n        `,\n                    border: `1px solid ${colors.border}`,\n                    position: \"relative\",\n                    overflow: \"hidden\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            height: \"100px\",\n                            background: `radial-gradient(ellipse at top, #FFE0B230 0%, transparent 70%)`,\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\",\n                            zIndex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    marginBottom: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            borderRadius: \"10px\",\n                                            background: `linear-gradient(135deg, ${colors.primary}20 0%, #FF8A6520 100%)`,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\"\n                                            },\n                                            children: \"\\uD83C\\uDFAF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: colors.text.primary,\n                                            margin: 0,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"Today's Mission\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"4px 12px\",\n                                            background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                            borderRadius: \"12px\",\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"AI Generated\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: colors.text.secondary,\n                                    fontSize: \"18px\",\n                                    lineHeight: \"1.6\",\n                                    margin: 0,\n                                    marginBottom: \"24px\"\n                                },\n                                children: '\"Focus on engaging with your audience about AI productivity tools. Your recent posts about automation got 3x more engagement. Consider sharing a behind-the-scenes story about how AI helps your workflow.\"'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(3, 1fr)\",\n                                    gap: \"20px\",\n                                    marginBottom: \"24px\"\n                                },\n                                children: [\n                                    {\n                                        label: \"Engagement Rate\",\n                                        value: \"+24%\",\n                                        icon: \"\\uD83D\\uDCC8\"\n                                    },\n                                    {\n                                        label: \"New Followers\",\n                                        value: \"127\",\n                                        icon: \"\\uD83D\\uDC65\"\n                                    },\n                                    {\n                                        label: \"Content Score\",\n                                        value: \"8.9/10\",\n                                        icon: \"⭐\"\n                                    }\n                                ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%)`,\n                                            borderRadius: \"16px\",\n                                            padding: \"20px\",\n                                            textAlign: \"center\",\n                                            backdropFilter: \"blur(10px)\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.5)\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    marginBottom: \"8px\"\n                                                },\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"700\",\n                                                    color: colors.text.primary,\n                                                    marginBottom: \"4px\"\n                                                },\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"500\"\n                                                },\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"16px\",\n                                    flexWrap: \"wrap\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/meeting\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                                color: \"white\",\n                                                border: \"none\",\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                boxShadow: `0 8px 24px ${colors.primary}40`,\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"\\uD83C\\uDFA5 Join Call\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"16px 24px\",\n                                            background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                            color: colors.text.primary,\n                                            border: `1px solid ${colors.border}`,\n                                            borderRadius: \"16px\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"600\",\n                                            cursor: \"pointer\",\n                                            backdropFilter: \"blur(10px)\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        children: \"\\uD83E\\uDD16 Ask Mentor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/tweet-center\",\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                padding: \"16px 24px\",\n                                                background: `linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%)`,\n                                                color: colors.text.primary,\n                                                border: `1px solid ${colors.border}`,\n                                                borderRadius: \"16px\",\n                                                fontSize: \"16px\",\n                                                fontWeight: \"600\",\n                                                cursor: \"pointer\",\n                                                backdropFilter: \"blur(10px)\",\n                                                transition: \"all 0.3s ease\"\n                                            },\n                                            children: \"✍️ Generate Tweet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: `linear-gradient(135deg, ${colors.surface} 0%, #FFE0B215 100%)`,\n                    borderRadius: \"20px\",\n                    padding: \"24px\",\n                    boxShadow: `\n          0 12px 40px rgba(0, 0, 0, 0.06),\n          inset 0 1px 0 rgba(255, 255, 255, 0.8)\n        `,\n                    border: `1px solid ${colors.border}`\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"16px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"64px\",\n                                height: \"64px\",\n                                borderRadius: \"20px\",\n                                background: `linear-gradient(135deg, ${colors.primary} 0%, #FF8A65 100%)`,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                boxShadow: `0 8px 24px ${colors.primary}30`,\n                                position: \"relative\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"32px\"\n                                    },\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        position: \"absolute\",\n                                        bottom: \"-2px\",\n                                        right: \"-2px\",\n                                        width: \"16px\",\n                                        height: \"16px\",\n                                        borderRadius: \"50%\",\n                                        background: \"#00E676\",\n                                        border: \"2px solid white\",\n                                        boxShadow: \"0 0 8px rgba(0, 230, 118, 0.6)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: colors.text.primary,\n                                        margin: 0,\n                                        fontSize: \"18px\",\n                                        fontWeight: \"600\",\n                                        marginBottom: \"4px\"\n                                    },\n                                    children: \"AI Mentor\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: colors.text.secondary,\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: '\"Ready to help you create content that resonates. What\\'s on your mind today?\"'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\nHomePage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/index.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();