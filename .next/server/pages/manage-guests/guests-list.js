/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/manage-guests/guests-list";
exports.ids = ["pages/manage-guests/guests-list"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmanage-guests%2Fguests-list&preferredRegion=&absolutePagePath=.%2Fpages%2Fmanage-guests%2Fguests-list.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmanage-guests%2Fguests-list&preferredRegion=&absolutePagePath=.%2Fpages%2Fmanage-guests%2Fguests-list.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/manage-guests/guests-list.tsx */ \"./pages/manage-guests/guests-list.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/manage-guests/guests-list\",\n        pathname: \"/manage-guests/guests-list\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_manage_guests_guests_list_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmanage-guests%2Fguests-list&preferredRegion=&absolutePagePath=.%2Fpages%2Fmanage-guests%2Fguests-list.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst SidebarLayout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"daily-operation\": true,\n        \"manage-staff\": false,\n        \"manage-guests\": true\n    });\n    // Fixoria-inspired color palette\n    const colors = {\n        primary: \"#22C55E\",\n        primaryLight: \"#4ADE80\",\n        primaryDark: \"#16A34A\",\n        accent: \"#F0FDF4\",\n        surface: \"#FFFFFF\",\n        surfaceElevated: \"#FAFAFA\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\",\n            inverse: \"#FFFFFF\" // White text\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\",\n            primary: \"#22C55E\" // Primary colored border\n        },\n        sidebar: {\n            background: \"#FFFFFF\",\n            border: \"#E5E7EB\",\n            hover: \"#F9FAFB\",\n            active: \"#F0FDF4\"\n        }\n    };\n    const navigationSections = [\n        {\n            id: \"daily-operation\",\n            title: \"DAILY OPERATION\",\n            items: [\n                {\n                    href: \"/dashboard\",\n                    label: \"Dashboard\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    isActive: router.pathname === \"/dashboard\"\n                },\n                {\n                    href: \"/reservation\",\n                    label: \"Reservation\",\n                    icon: \"\\uD83D\\uDCC5\",\n                    isActive: router.pathname.startsWith(\"/reservation\"),\n                    hasSubmenu: true\n                },\n                {\n                    href: \"/room-operation\",\n                    label: \"Room Operation\",\n                    icon: \"\\uD83C\\uDFE0\",\n                    isActive: router.pathname === \"/room-operation\"\n                }\n            ]\n        },\n        {\n            id: \"manage-staff\",\n            title: \"MANAGE STAFF\",\n            items: [\n                {\n                    href: \"/manage-staff\",\n                    label: \"Manage Staff\",\n                    icon: \"\\uD83D\\uDC65\",\n                    isActive: router.pathname.startsWith(\"/manage-staff\"),\n                    hasSubmenu: true\n                }\n            ]\n        },\n        {\n            id: \"manage-guests\",\n            title: \"MANAGE GUESTS\",\n            items: [\n                {\n                    href: \"/manage-guests\",\n                    label: \"Manage Guests\",\n                    icon: \"\\uD83D\\uDC64\",\n                    isActive: router.pathname.startsWith(\"/manage-guests\"),\n                    hasSubmenu: true,\n                    subItems: [\n                        {\n                            href: \"/manage-guests/guests-list\",\n                            label: \"Guests List\",\n                            isActive: router.pathname === \"/manage-guests/guests-list\" || router.pathname === \"/meeting\"\n                        },\n                        {\n                            href: \"/manage-guests/reviews\",\n                            label: \"Guests Reviews\",\n                            isActive: router.pathname === \"/manage-guests/reviews\"\n                        }\n                    ]\n                }\n            ]\n        }\n    ];\n    const bottomSections = [\n        {\n            href: \"/promotions\",\n            label: \"Promotions\",\n            icon: \"\\uD83C\\uDFAF\",\n            isActive: router.pathname === \"/promotions\"\n        }\n    ];\n    const toggleSection = (sectionId)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [sectionId]: !prev[sectionId]\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: colors.background\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"240px\",\n                    background: colors.sidebar.background,\n                    borderRight: `1px solid ${colors.sidebar.border}`,\n                    minHeight: \"100vh\",\n                    position: \"relative\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: `1px solid ${colors.border.light}`,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"12px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"32px\",\n                                    height: \"32px\",\n                                    background: colors.primary,\n                                    borderRadius: \"6px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    position: \"relative\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"700\"\n                                    },\n                                    children: \"F\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        margin: 0,\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: colors.text.primary,\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: \"Fixoria ™\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginLeft: \"auto\",\n                                    width: \"20px\",\n                                    height: \"20px\",\n                                    background: colors.surfaceElevated,\n                                    borderRadius: \"4px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    cursor: \"pointer\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⚙\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderBottom: `1px solid ${colors.border.light}`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: \"8px\",\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"24px\",\n                                        height: \"24px\",\n                                        background: colors.primary,\n                                        borderRadius: \"50%\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"white\",\n                                            fontSize: \"12px\",\n                                            fontWeight: \"600\"\n                                        },\n                                        children: \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\",\n                                                color: colors.text.primary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"Grand Sylhet Hotel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"12px\",\n                                                color: colors.text.tertiary,\n                                                lineHeight: \"1.2\"\n                                            },\n                                            children: \"3 more hotels\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: colors.text.tertiary\n                                    },\n                                    children: \"⌄\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            flex: 1,\n                            padding: \"0\",\n                            overflow: \"auto\"\n                        },\n                        children: [\n                            navigationSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"24px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"8px 20px\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onClick: ()=>toggleSection(section.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"11px\",\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.tertiary,\n                                                        letterSpacing: \"0.5px\",\n                                                        textTransform: \"uppercase\"\n                                                    },\n                                                    children: section.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: colors.text.tertiary,\n                                                        transform: expandedSections[section.id] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.2s ease\"\n                                                    },\n                                                    children: \"⌄\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        expandedSections[section.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: \"8px\",\n                                                paddingRight: \"8px\"\n                                            },\n                                            children: section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: item.href,\n                                                            style: {\n                                                                textDecoration: \"none\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    padding: \"8px 12px\",\n                                                                    margin: \"2px 0\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: \"pointer\",\n                                                                    backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                                    transition: \"all 0.15s ease\"\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                    }\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    if (!item.isActive) {\n                                                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                    }\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"16px\",\n                                                                            marginRight: \"12px\",\n                                                                            width: \"20px\",\n                                                                            textAlign: \"center\"\n                                                                        },\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"14px\",\n                                                                            fontWeight: item.isActive ? \"600\" : \"500\",\n                                                                            color: item.isActive ? colors.text.primary : colors.text.secondary,\n                                                                            flex: 1\n                                                                        },\n                                                                        children: item.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    item.hasSubmenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            fontSize: \"12px\",\n                                                                            color: colors.text.tertiary,\n                                                                            transform: item.subItems && expandedSections[`${section.id}-${item.href}`] ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                                            transition: \"transform 0.2s ease\"\n                                                                        },\n                                                                        children: \"⌄\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        item.subItems && item.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                paddingLeft: \"32px\",\n                                                                marginTop: \"4px\",\n                                                                marginBottom: \"8px\"\n                                                            },\n                                                            children: item.subItems.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: subItem.href,\n                                                                    style: {\n                                                                        textDecoration: \"none\"\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            padding: \"6px 12px\",\n                                                                            margin: \"1px 0\",\n                                                                            borderRadius: \"4px\",\n                                                                            cursor: \"pointer\",\n                                                                            backgroundColor: subItem.isActive ? colors.sidebar.active : \"transparent\",\n                                                                            borderLeft: subItem.isActive ? `2px solid ${colors.primary}` : \"2px solid transparent\",\n                                                                            transition: \"all 0.15s ease\"\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                                            }\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            if (!subItem.isActive) {\n                                                                                e.currentTarget.style.backgroundColor = \"transparent\";\n                                                                            }\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: subItem.isActive ? \"600\" : \"500\",\n                                                                                color: subItem.isActive ? colors.text.primary : colors.text.secondary\n                                                                            },\n                                                                            children: subItem.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, subItem.href, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, item.href, true, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, section.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"8px\"\n                                },\n                                children: bottomSections.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                padding: \"8px 12px\",\n                                                margin: \"2px 0\",\n                                                borderRadius: \"6px\",\n                                                cursor: \"pointer\",\n                                                backgroundColor: item.isActive ? colors.sidebar.active : \"transparent\",\n                                                transition: \"all 0.15s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                                }\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                if (!item.isActive) {\n                                                    e.currentTarget.style.backgroundColor = \"transparent\";\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"16px\",\n                                                        marginRight: \"12px\",\n                                                        width: \"20px\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: item.isActive ? \"600\" : \"500\",\n                                                        color: item.isActive ? colors.text.primary : colors.text.secondary\n                                                    },\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"16px 20px\",\n                            borderTop: `1px solid ${colors.border.light}`,\n                            marginTop: \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"ACCOUNTING\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            padding: \"8px 0\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"11px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.tertiary,\n                                                    letterSpacing: \"0.5px\",\n                                                    textTransform: \"uppercase\"\n                                                },\n                                                children: \"SYSTEM OPTIONS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary,\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/notifications\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"\\uD83D\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary,\n                                                flex: 1\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"18px\",\n                                                height: \"18px\",\n                                                borderRadius: \"50%\",\n                                                background: \"#EF4444\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                fontSize: \"11px\",\n                                                color: \"white\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/support\",\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 12px\",\n                                        margin: \"2px 0\",\n                                        borderRadius: \"6px\",\n                                        cursor: \"pointer\",\n                                        backgroundColor: \"transparent\",\n                                        transition: \"all 0.15s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = colors.sidebar.hover;\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"transparent\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"16px\",\n                                                marginRight: \"12px\",\n                                                width: \"20px\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"❓\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"14px\",\n                                                fontWeight: \"500\",\n                                                color: colors.text.secondary\n                                            },\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\",\n                                    padding: \"12px 8px\",\n                                    marginTop: \"8px\",\n                                    cursor: \"pointer\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            background: colors.primary,\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"white\",\n                                                fontSize: \"14px\",\n                                                fontWeight: \"600\"\n                                            },\n                                            children: \"R\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.primary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Rahat Ali\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"12px\",\n                                                    color: colors.text.tertiary,\n                                                    lineHeight: \"1.2\"\n                                                },\n                                                children: \"Super Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    backgroundColor: colors.background,\n                    minHeight: \"100vh\",\n                    position: \"relative\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 619,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/manage-guests/guests-list.tsx":
/*!*********************************************!*\
  !*** ./pages/manage-guests/guests-list.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst GuestsListPage = ()=>{\n    const [selectedGuests, setSelectedGuests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        staff: \"All Staff\",\n        status: \"All\",\n        monthly: \"Monthly\"\n    });\n    // Fixoria-inspired color palette\n    const colors = {\n        primary: \"#22C55E\",\n        primaryLight: \"#4ADE80\",\n        surface: \"#FFFFFF\",\n        background: \"#F8FAFC\",\n        text: {\n            primary: \"#1F2937\",\n            secondary: \"#6B7280\",\n            tertiary: \"#9CA3AF\",\n            muted: \"#D1D5DB\"\n        },\n        border: {\n            light: \"#E5E7EB\",\n            medium: \"#D1D5DB\"\n        },\n        status: {\n            paid: \"#22C55E\",\n            pending: \"#F59E0B\",\n            online: \"#3B82F6\"\n        }\n    };\n    const guestData = [\n        {\n            id: \"#102112\",\n            name: \"Raclora Kae\",\n            avatar: \"\\uD83D\\uDC69\",\n            source: \"Font Desks\",\n            date: \"20/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703570\",\n            amount: \"$1400.00\",\n            status: \"Cash - Paid\",\n            statusType: \"paid\"\n        },\n        {\n            id: \"#102111\",\n            name: \"Jessy Mac\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Web Reservation\",\n            date: \"19/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01966703578\",\n            amount: \"$400.00\",\n            status: \"Cash - Paid\",\n            statusType: \"paid\"\n        },\n        {\n            id: \"#102110\",\n            name: \"Maccullam Brad\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Font Desks\",\n            date: \"18/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01866703570\",\n            amount: \"$9800.00\",\n            status: \"Pending\",\n            statusType: \"pending\"\n        },\n        {\n            id: \"#102109\",\n            name: \"Jhoney Clark\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Group Reservation\",\n            date: \"17/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01966703570\",\n            amount: \"$2400.00\",\n            status: \"Online - Paid\",\n            statusType: \"online\"\n        },\n        {\n            id: \"#102108\",\n            name: \"Jhon Brak\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Font Desks\",\n            date: \"16/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703544\",\n            amount: \"$1400.00\",\n            status: \"Pending\",\n            statusType: \"pending\"\n        },\n        {\n            id: \"#102107\",\n            name: \"Red Alone\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Group Reservation\",\n            date: \"15/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703533\",\n            amount: \"$980.00\",\n            status: \"Cash - Paid\",\n            statusType: \"paid\"\n        },\n        {\n            id: \"#102106\",\n            name: \"Susie Rayden\",\n            avatar: \"\\uD83D\\uDC69\",\n            source: \"Web Reservation\",\n            date: \"13/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703570\",\n            amount: \"$854.00\",\n            status: \"Cash - Paid\",\n            statusType: \"paid\"\n        },\n        {\n            id: \"#102105\",\n            name: \"Mark Alone\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Font Desks\",\n            date: \"11/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703599\",\n            amount: \"$187.00\",\n            status: \"Cash - Paid\",\n            statusType: \"paid\"\n        },\n        {\n            id: \"#102104\",\n            name: \"Mukkaram Bari\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Web Reservation\",\n            date: \"10/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703588\",\n            amount: \"$1870.00\",\n            status: \"Pending\",\n            statusType: \"pending\"\n        },\n        {\n            id: \"#102103\",\n            name: \"Shaniyar Khan\",\n            avatar: \"\\uD83D\\uDC68\",\n            source: \"Font Desks\",\n            date: \"08/12/2024\",\n            email: \"<EMAIL>\",\n            mobile: \"+88 01766703570\",\n            amount: \"$1900.00\",\n            status: \"Online - Paid\",\n            statusType: \"online\"\n        }\n    ];\n    const toggleGuestSelection = (guestId)=>{\n        setSelectedGuests((prev)=>prev.includes(guestId) ? prev.filter((id)=>id !== guestId) : [\n                ...prev,\n                guestId\n            ]);\n    };\n    const toggleSelectAll = ()=>{\n        setSelectedGuests((prev)=>prev.length === guestData.length ? [] : guestData.map((guest)=>guest.id));\n    };\n    const getStatusColor = (statusType)=>{\n        switch(statusType){\n            case \"paid\":\n                return colors.status.paid;\n            case \"pending\":\n                return colors.status.pending;\n            case \"online\":\n                return colors.status.online;\n            default:\n                return colors.text.tertiary;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            backgroundColor: colors.background,\n            padding: \"24px 32px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    marginBottom: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\",\n                            fontSize: \"14px\",\n                            color: colors.text.tertiary\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"\\uD83C\\uDFE0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Manage Guests\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    color: colors.text.primary,\n                                    fontWeight: \"600\"\n                                },\n                                children: \"Guests List\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        style: {\n                            background: colors.primary,\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            padding: \"10px 16px\",\n                            fontSize: \"14px\",\n                            fontWeight: \"600\",\n                            cursor: \"pointer\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"8px\"\n                        },\n                        children: \"+ Add New Guests\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: colors.surface,\n                    borderRadius: \"12px\",\n                    border: `1px solid ${colors.border.light}`,\n                    overflow: \"hidden\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"24px 32px\",\n                            borderBottom: `1px solid ${colors.border.light}`,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"16px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: \"48px\",\n                                    height: \"48px\",\n                                    background: colors.background,\n                                    borderRadius: \"12px\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"24px\"\n                                },\n                                children: \"\\uD83D\\uDC65\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"24px\",\n                                            fontWeight: \"700\",\n                                            color: colors.text.primary,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"8px\"\n                                        },\n                                        children: [\n                                            \"Guests List\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"16px\",\n                                                    color: colors.text.tertiary\n                                                },\n                                                children: \"⭐\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            margin: \"4px 0 0 0\",\n                                            fontSize: \"14px\",\n                                            color: colors.text.tertiary\n                                        },\n                                        children: \"Auto-updates in 2 min\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 32px\",\n                            borderBottom: `1px solid ${colors.border.light}`,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            gap: \"16px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    Object.entries(filters).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: value,\n                                            onChange: (e)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        [key]: e.target.value\n                                                    })),\n                                            style: {\n                                                padding: \"8px 12px\",\n                                                border: `1px solid ${colors.border.medium}`,\n                                                borderRadius: \"6px\",\n                                                fontSize: \"14px\",\n                                                backgroundColor: colors.surface,\n                                                color: colors.text.primary,\n                                                cursor: \"pointer\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: value,\n                                                children: value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, key, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, undefined)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"8px\",\n                                            border: `1px solid ${colors.border.medium}`,\n                                            borderRadius: \"6px\",\n                                            backgroundColor: colors.surface,\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\"\n                                        },\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"relative\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search...\",\n                                                style: {\n                                                    padding: \"8px 12px 8px 36px\",\n                                                    border: `1px solid ${colors.border.medium}`,\n                                                    borderRadius: \"6px\",\n                                                    fontSize: \"14px\",\n                                                    backgroundColor: colors.surface,\n                                                    width: \"200px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    left: \"12px\",\n                                                    top: \"50%\",\n                                                    transform: \"translateY(-50%)\",\n                                                    fontSize: \"14px\",\n                                                    color: colors.text.tertiary\n                                                },\n                                                children: \"\\uD83D\\uDD0D\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"8px 12px\",\n                                            border: `1px solid ${colors.border.medium}`,\n                                            borderRadius: \"6px\",\n                                            backgroundColor: colors.surface,\n                                            fontSize: \"14px\",\n                                            cursor: \"pointer\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"6px\"\n                                        },\n                                        children: \"\\uD83D\\uDCC4 Export PDF\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            padding: \"8px 12px\",\n                                            border: `1px solid ${colors.border.medium}`,\n                                            borderRadius: \"6px\",\n                                            backgroundColor: colors.surface,\n                                            fontSize: \"14px\",\n                                            cursor: \"pointer\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"6px\"\n                                        },\n                                        children: \"\\uD83D\\uDCCA Export Excel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            overflow: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            style: {\n                                width: \"100%\",\n                                borderCollapse: \"collapse\",\n                                fontSize: \"14px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            backgroundColor: colors.background\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`,\n                                                    width: \"50px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedGuests.length === guestData.length,\n                                                    onChange: toggleSelectAll,\n                                                    style: {\n                                                        cursor: \"pointer\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Booking No\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Name of Guest\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Mobile Number\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"left\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`\n                                                },\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    textAlign: \"center\",\n                                                    fontWeight: \"600\",\n                                                    color: colors.text.secondary,\n                                                    borderBottom: `1px solid ${colors.border.light}`,\n                                                    width: \"50px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: guestData.map((guest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            style: {\n                                                backgroundColor: index % 2 === 0 ? colors.surface : colors.background,\n                                                transition: \"background-color 0.15s ease\"\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.currentTarget.style.backgroundColor = \"#F9FAFB\";\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.currentTarget.style.backgroundColor = index % 2 === 0 ? colors.surface : colors.background;\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: selectedGuests.includes(guest.id),\n                                                        onChange: ()=>toggleGuestSelection(guest.id),\n                                                        style: {\n                                                            cursor: \"pointer\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.primary\n                                                    },\n                                                    children: guest.id\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: \"12px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: \"32px\",\n                                                                    height: \"32px\",\n                                                                    borderRadius: \"50%\",\n                                                                    background: colors.primary,\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"center\",\n                                                                    fontSize: \"16px\"\n                                                                },\n                                                                children: guest.avatar\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontWeight: \"600\",\n                                                                    color: colors.text.primary\n                                                                },\n                                                                children: guest.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        color: colors.text.secondary\n                                                    },\n                                                    children: guest.source\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        color: colors.text.secondary\n                                                    },\n                                                    children: guest.date\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        color: colors.text.secondary\n                                                    },\n                                                    children: guest.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        color: colors.text.secondary\n                                                    },\n                                                    children: guest.mobile\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        fontWeight: \"600\",\n                                                        color: colors.text.primary\n                                                    },\n                                                    children: guest.amount\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            padding: \"4px 8px\",\n                                                            borderRadius: \"4px\",\n                                                            fontSize: \"12px\",\n                                                            fontWeight: \"600\",\n                                                            backgroundColor: `${getStatusColor(guest.statusType)}20`,\n                                                            color: getStatusColor(guest.statusType)\n                                                        },\n                                                        children: guest.status\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"16px 24px\",\n                                                        borderBottom: `1px solid ${colors.border.light}`,\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        style: {\n                                                            background: \"none\",\n                                                            border: \"none\",\n                                                            cursor: \"pointer\",\n                                                            fontSize: \"16px\",\n                                                            color: colors.text.tertiary,\n                                                            padding: \"4px\"\n                                                        },\n                                                        children: \"⋯\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, guest.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"20px 32px\",\n                            borderTop: `1px solid ${colors.border.light}`,\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                style: {\n                                    padding: \"8px 16px\",\n                                    border: `1px solid ${colors.border.medium}`,\n                                    borderRadius: \"6px\",\n                                    backgroundColor: colors.surface,\n                                    fontSize: \"14px\",\n                                    cursor: \"pointer\",\n                                    color: colors.text.secondary\n                                },\n                                children: \"← Previous\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"8px\"\n                                },\n                                children: [\n                                    1,\n                                    2,\n                                    10,\n                                    12,\n                                    13,\n                                    14\n                                ].map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            width: \"32px\",\n                                            height: \"32px\",\n                                            border: page === 1 ? `1px solid ${colors.primary}` : `1px solid ${colors.border.medium}`,\n                                            borderRadius: \"6px\",\n                                            backgroundColor: page === 1 ? colors.primary : colors.surface,\n                                            color: page === 1 ? \"white\" : colors.text.secondary,\n                                            fontSize: \"14px\",\n                                            cursor: \"pointer\",\n                                            fontWeight: page === 1 ? \"600\" : \"400\"\n                                        },\n                                        children: page\n                                    }, page, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                style: {\n                                    padding: \"8px 16px\",\n                                    border: `1px solid ${colors.border.medium}`,\n                                    borderRadius: \"6px\",\n                                    backgroundColor: colors.surface,\n                                    fontSize: \"14px\",\n                                    cursor: \"pointer\",\n                                    color: colors.text.secondary\n                                },\n                                children: \"Next →\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\nGuestsListPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/manage-guests/guests-list.tsx\",\n        lineNumber: 713,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GuestsListPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/manage-guests/guests-list.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmanage-guests%2Fguests-list&preferredRegion=&absolutePagePath=.%2Fpages%2Fmanage-guests%2Fguests-list.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();