/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/meeting";
exports.ids = ["pages/meeting"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/meeting.tsx */ \"./pages/meeting.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/meeting\",\n        pathname: \"/meeting\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/MeetingControls.tsx":
/*!****************************************!*\
  !*** ./components/MeetingControls.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst MeetingControls = ({ micOn, cameraOn, onToggleMic, onToggleCamera, onLeave, onTalkToExie, exieLoading })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            gap: \"16px\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            padding: \"16px 24px\",\n            backgroundColor: \"#fff\",\n            borderRadius: \"8px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n            margin: \"16px 0\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggleMic,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: micOn ? \"#0070f3\" : \"#ccc\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: micOn ? \"\\uD83C\\uDFA4\" : \"\\uD83D\\uDD07\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/MeetingControls.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggleCamera,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: cameraOn ? \"#0070f3\" : \"#ccc\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: cameraOn ? \"\\uD83D\\uDCF7\" : \"\\uD83D\\uDEAB\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/MeetingControls.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onTalkToExie,\n                disabled: exieLoading,\n                style: {\n                    backgroundColor: exieLoading ? \"#ccc\" : \"var(--accent-color)\",\n                    color: \"#fff\",\n                    padding: \"12px 24px\",\n                    borderRadius: \"4px\",\n                    border: \"none\",\n                    cursor: exieLoading ? \"not-allowed\" : \"pointer\",\n                    fontSize: \"16px\",\n                    fontWeight: 600\n                },\n                children: exieLoading ? \"Exie is thinking...\" : \"Talk to Exie\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/MeetingControls.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onLeave,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: \"#e74c3c\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/MeetingControls.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/MeetingControls.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MeetingControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/MeetingControls.tsx\n");

/***/ }),

/***/ "./components/SidebarLayout.tsx":
/*!**************************************!*\
  !*** ./components/SidebarLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SidebarLayout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    backgroundColor: \"#f0f0f0\",\n                    padding: \"16px\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            listStyle: \"none\",\n                            padding: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/tweet-center\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Tweet Center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/meeting\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"AI Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    padding: \"24px\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/SidebarLayout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./components/VideoCallGrid.tsx":
/*!**************************************!*\
  !*** ./components/VideoCallGrid.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @daily-co/daily-react */ \"@daily-co/daily-react\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Using a more neutral placeholder for a white theme\nconst EXIE_VIDEO_PLACEHOLDER = \"https://via.placeholder.com/320x240.png?text=Exie+AI\";\n// Update component signature to accept props\nconst VideoCallGrid = ({ joined, micOn, cameraOn, exieLoading, exieVideoUrl, exieTranscript, onToggleMic, onToggleCamera, onLeave, onTalkToExie })=>{\n    const room = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useRoom)();\n    const localParticipant = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useLocalParticipant)();\n    // Get IDs of all participants, excluding the local participant\n    const participantIds = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useParticipantIds)();\n    const remoteParticipantIds = participantIds.filter((id)=>id !== localParticipant?.session_id);\n    // Handlers for controls - these just call the parent-provided handlers\n    // These are not directly used in this component's render but are available if needed.\n    // const handleToggleMicClick = () => { onToggleMic(); };\n    // const handleToggleCameraClick = () => { onToggleCamera(); };\n    // const handleLeaveClick = async () => { onLeave(); };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"grid\",\n            gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n            gap: 16,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            padding: 16,\n            backgroundColor: \"#ffffff\",\n            borderRadius: 8,\n            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n            flexGrow: 1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    border: \"1px solid rgba(0, 123, 255, 0.3)\",\n                    borderRadius: 8,\n                    overflow: \"hidden\",\n                    width: \"100%\",\n                    paddingTop: \"75%\",\n                    position: \"relative\",\n                    background: \"#eee\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    exieLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#555\",\n                            textAlign: \"center\",\n                            margin: \"auto\"\n                        },\n                        children: \"Exie is thinking...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined) // Darker text\n                     : exieVideoUrl ? // Use controls only for debugging, remove for final version\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        src: exieVideoUrl,\n                        autoPlay: true,\n                        playsInline: true,\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: EXIE_VIDEO_PLACEHOLDER,\n                        alt: \"Exie AI\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            textAlign: \"center\",\n                            background: \"rgba(255,255,255,0.7)\",\n                            color: \"#333\",\n                            fontWeight: 600,\n                            padding: \"4px 0\"\n                        },\n                        children: \"Exie (AI)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    exieTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"#fff\",\n                            padding: 8,\n                            fontSize: 14,\n                            maxHeight: \"50%\",\n                            overflowY: \"auto\"\n                        },\n                        children: exieTranscript\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"exie-ai\", true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            localParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    border: \"1px solid rgba(0, 123, 255, 0.5)\",\n                    borderRadius: 8,\n                    overflow: \"hidden\",\n                    width: \"100%\",\n                    paddingTop: \"75%\",\n                    position: \"relative\",\n                    background: \"#eee\"\n                },\n                children: [\n                    localParticipant.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyVideo, {\n                        sessionId: localParticipant.session_id,\n                        type: \"video\",\n                        autoPlay: true,\n                        // The DailyVideo component handles attaching the track internally\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            color: \"#555\"\n                        },\n                        children: \"Camera Off\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            textAlign: \"center\",\n                            background: \"rgba(255,255,255,0.7)\",\n                            color: \"var(--accent-color)\",\n                            fontWeight: 600,\n                            padding: \"4px 0\"\n                        },\n                        children: \"You\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, localParticipant.session_id, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined),\n            remoteParticipantIds.map((id)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RemoteParticipantVideo, {\n                    participantId: id\n                }, id, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, undefined);\n            })\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper component to render remote participant video\nconst RemoteParticipantVideo = ({ participantId })=>{\n    const participant = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useParticipant)(participantId);\n    // Use DailyVideo component for remote participants\n    if (!participant || !participant.video) {\n        return null; // Explicitly return null if no video to render or if track is boolean (e.g., false)\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            border: \"1px solid rgba(0, 123, 255, 0.3)\",\n            borderRadius: 8,\n            overflow: \"hidden\",\n            width: \"100%\",\n            paddingTop: \"75%\",\n            position: \"relative\",\n            background: \"#eee\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyVideo, {\n                sessionId: participant.session_id,\n                type: \"video\",\n                autoPlay: true,\n                style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    height: \"100%\",\n                    objectFit: \"cover\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    bottom: 0,\n                    left: 0,\n                    right: 0,\n                    textAlign: \"center\",\n                    background: \"rgba(255,255,255,0.7)\",\n                    color: \"#333\",\n                    fontWeight: 600,\n                    padding: \"4px 0\"\n                },\n                children: [\n                    \"User \",\n                    participant.user_name || participant.session_id.substring(0, 8)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            \" \"\n        ]\n    }, participant.session_id, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/components/VideoCallGrid.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoCallGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/VideoCallGrid.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0IsQ0FBQyxnQkFBZ0I7QUFhaEQsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHR5cGUgeyBSZWFjdEVsZW1lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgTmV4dFBhZ2UgfSBmcm9tICduZXh0JztcblxuZXhwb3J0IHR5cGUgTmV4dFBhZ2VXaXRoTGF5b3V0ID0gTmV4dFBhZ2UgJiB7XG4gIGdldExheW91dD86IChwYWdlOiBSZWFjdEVsZW1lbnQpID0+IFJlYWN0Tm9kZTtcbn07XG5cbmV4cG9ydCB0eXBlIEFwcFByb3BzV2l0aExheW91dCA9IEFwcFByb3BzICYge1xuICBDb21wb25lbnQ6IE5leHRQYWdlV2l0aExheW91dDtcbn07XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHNXaXRoTGF5b3V0KSB7XG4gIC8vIFVzZSB0aGUgbGF5b3V0IGRlZmluZWQgYXQgdGhlIHBhZ2UgbGV2ZWwsIGlmIGF2YWlsYWJsZVxuICBjb25zdCBnZXRMYXlvdXQgPSBDb21wb25lbnQuZ2V0TGF5b3V0IHx8ICgocGFnZSkgPT4gcGFnZSk7XG5cbiAgcmV0dXJuIGdldExheW91dCg8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgTXlBcHA7Il0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZ2V0TGF5b3V0IiwicGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_VideoCallGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/VideoCallGrid */ \"./components/VideoCallGrid.tsx\");\n/* harmony import */ var _components_MeetingControls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/MeetingControls */ \"./components/MeetingControls.tsx\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @daily-co/daily-react */ \"@daily-co/daily-react\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_SidebarLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/SidebarLayout */ \"./components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst MeetingPage = ()=>{\n    // Bring back the state for controls and Exie interaction\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [micOn, setMicOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [cameraOn, setCameraOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [exieLoading, setExieLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exieVideoUrl, setExieVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Exie's video URL\n    const [exieTranscript, setExieTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Exie's transcript\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Create Daily.co room using the API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const createRoom = async ()=>{\n            try {\n                // Create a room server-side using the Daily.co API\n                const response = await fetch(\"/api/daily-room\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        roomName: \"exie-meeting-\" + Date.now()\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to create room\");\n                }\n                const data = await response.json();\n                setRoomUrl(data.url);\n                setJoined(false); // Start as not joined, will join when user clicks join\n            } catch (error) {\n                console.error(\"Error creating room:\", error);\n                // Fallback to a demo room URL for testing\n                const fallbackUrl = \"https://exie-ai.daily.co/exie-demo-room\";\n                setRoomUrl(fallbackUrl);\n                setJoined(false);\n            }\n        };\n        createRoom();\n    }, []); // Run once on component mount\n    // Handlers for controls - Implement the logic here\n    const handleToggleMic = async ()=>{\n        // Toggle mic logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setMicOn(!micOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the micOn prop.\n    };\n    const handleToggleCamera = async ()=>{\n        // Toggle camera logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setCameraOn(!cameraOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the cameraOn prop.\n    };\n    const handleJoin = async ()=>{\n        setJoined(true);\n    };\n    const handleLeave = async ()=>{\n        setJoined(false);\n    // Optionally reload to reset state\n    // window.location.reload();\n    };\n    // Handle Talk to Exie logic (re-implemented here)\n    const handleTalkToExie = async ()=>{\n        if (!micOn || exieLoading) return; // Ensure mic state is on and not already loading\n        setExieLoading(true);\n        setExieTranscript(\"\"); // Clear previous transcript\n        setExieVideoUrl(null); // Clear previous video\n        console.log(\"Starting audio recording...\");\n        let mediaRecorder = null;\n        let stream = null;\n        try {\n            // Get a new audio stream specifically for recording\n            stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm\"\n            });\n            const audioChunks = [];\n            mediaRecorder.ondataavailable = (e)=>{\n                if (e.data.size > 0) {\n                    audioChunks.push(e.data);\n                    console.log(\"Collected audio chunk:\", e.data.size, \"bytes\");\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                console.log(\"Audio recording stopped. Total chunks:\", audioChunks.length);\n                const audioBlob = new Blob(audioChunks, {\n                    type: \"audio/webm\"\n                });\n                console.log(\"Audio Blob created:\", audioBlob.size, \"bytes\", audioBlob.type);\n                // Send to /api/transcribe\n                console.log(\"Sending audio to /api/transcribe...\");\n                const res = await fetch(\"/api/transcribe\", {\n                    method: \"POST\",\n                    body: audioBlob,\n                    headers: {\n                        \"Content-Type\": \"audio/webm\"\n                    }\n                });\n                if (!res.ok) throw new Error(`Transcribe API error: ${res.status}`);\n                const data = await res.json();\n                const transcript = data.transcript || \"\";\n                console.log(\"Transcript received:\", transcript);\n                setExieTranscript(transcript); // Update Exie\\'s transcript state\n                if (transcript) {\n                    // Send to AI pipeline (gpt -> voice -> video)\n                    console.log(\"Sending transcript to /api/gpt...\");\n                    const gptRes = await fetch(\"/api/gpt\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            message: transcript,\n                            tweetsOrHandle: \"\"\n                        })\n                    });\n                    if (!gptRes.ok) throw new Error(`GPT API error: ${gptRes.status}`);\n                    const gptData = await gptRes.json();\n                    const mentorReply = gptData.reply || \"Sorry, I could not generate a response.\";\n                    console.log(\"GPT Reply received:\", mentorReply);\n                    setExieTranscript(mentorReply); // Update Exie\\'s transcript state with reply\n                    // ElevenLabs\n                    console.log(\"Sending text to /api/voice...\");\n                    const voiceRes = await fetch(\"/api/voice\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            text: mentorReply\n                        })\n                    });\n                    if (!voiceRes.ok) throw new Error(`Voice API error: ${voiceRes.status}`);\n                    const voiceData = await voiceRes.json();\n                    console.log(\"Voice data received (base64 length):\", voiceData.audioBase64?.length);\n                    if (!voiceData.audioBase64) throw new Error(\"No audio returned from ElevenLabs.\");\n                    // Create a blob URL for the audio\n                    const audioBlob = new Blob([\n                        Uint8Array.from(atob(voiceData.audioBase64), (c)=>c.charCodeAt(0))\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioBlob);\n                    console.log(\"Audio Blob URL created:\", audioUrl);\n                    // D-ID\n                    console.log(\"Sending audio URL to /api/video...\");\n                    const videoRes = await fetch(\"/api/video\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            audioUrl,\n                            imageUrl: \"https://via.placeholder.com/320x240.png?text=Exie+AI\"\n                        }) // Use consistent placeholder\n                    });\n                    if (!videoRes.ok) throw new Error(`Video API error: ${videoRes.status}`);\n                    const videoData = await videoRes.json();\n                    console.log(\"Video URL received:\", videoData.videoUrl);\n                    if (videoData.videoUrl) setExieVideoUrl(videoData.videoUrl); // Update Exie\\'s video URL state\n                    // Play the audio\n                    const audio = new Audio(audioUrl);\n                    audio.play();\n                } else {\n                    console.log(\"No transcript to process.\");\n                }\n            };\n            mediaRecorder.start();\n            // Stop recording after a short delay (adjust as needed)\n            setTimeout(()=>{\n                if (mediaRecorder?.state !== \"inactive\") mediaRecorder?.stop();\n                // Stop the getUserMedia tracks after recording stops\n                stream?.getTracks().forEach((track)=>track.stop());\n            }, 5000); // Record for 5 seconds max\n        } catch (error) {\n            console.error(\"Error recording audio or processing AI pipeline:\", error);\n            setExieTranscript(\"Sorry, there was an error processing your request.\"); // Update Exie\\'s transcript state on error\n        } finally{\n            setExieLoading(false);\n        }\n    };\n    // Effect to join the Agora channel when component mounts and agoraClient is ready (now handled in VideoCallGrid)\n    // However, we need to set \\'joined\\' state based on connection status.\n    // We\\'ll rely on VideoCallGrid to emit a \\'joined\\' status change or similar if needed, or manage joining entirely in VideoCallGrid\\'s hook.\n    // For simplicity now, assume VideoCallGrid\\'s useJoin hook is sufficient to manage the connection, and we\\'ll rely on the mic/camera tracks being ready to indicate a potential \\'joined\\' state for controls visibility.\n    // A more robust approach might involve a context or callback from VideoCallGrid when truly connected.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simple way to set joined state based on whether local tracks are available\n        if (micOn && cameraOn) {\n        // This is a simplification. A real \\'joined\\' status should come from Agora\\'s connection state.\n        // We will pass the \\'joined\\' prop down, and VideoCallGrid\\'s useJoin will attempt to join.\n        // We might need a mechanism to get the actual connection state back up from VideoCallGrid.\n        // For now, let\\'s rely on VideoCallGrid\\'s internal joined state derived from the Agora client listener\n        // and pass down the mic/camera state and handlers.\n        // The \\'joined\\' state in MeetingPage will primarily control visibility of the footer controls.\n        // Re-add connection state listener if not fully handled by VideoCallGrid\n        // If VideoCallGrid\\'s useJoin manages connection and updates an internal state,\n        // we might need a way for VideoCallGrid to communicate the joined status up.\n        // Let\\'s stick to the current plan: VideoCallGrid handles Agora, MeetingPage handles overall UI and AI pipeline.\n        // MeetingPage\\'s \\'joined\\' state will primarily control visibility of the footer controls.\n        // A potential approach: VideoCallGrid calls a prop function like onJoinedStatusChange(status: boolean)\n        // useEffect(() => {\n        //   if (agoraClient) {\n        //     agoraClient.on(\\'connection-state-change\\', (state) => {\n        //       if (state === \\'CONNECTED\\') {\n        //         setJoined(true);\n        //       } else {\n        //         setJoined(false);\n        //       }\n        //     });\n        //   }\n        // }, [agoraClient]);\n        // For Daily.co, the useRoom hook\\'s state might be a better indicator of joined status.\n        // Let\\'s update the joined state based on the presence of a room URL for now, as done in the initial effect.\n        }\n    }, [\n        micOn,\n        cameraOn\n    ]); // Depend on micOn and cameraOn to re-check joined status (simplified)\n    // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"var(--accent-color)\",\n                            marginBottom: \"16px\"\n                        },\n                        children: \"Setting up your meeting...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#666\"\n                        },\n                        children: \"Please wait while we prepare your room\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, undefined);\n    }\n    return(// Wrap the meeting content in DailyProvider to provide the Daily room context to children\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__.DailyProvider, {\n        url: roomUrl,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: [\n                \" \",\n                !joined ? // Show join screen when not joined\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        height: \"100vh\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        flexDirection: \"column\",\n                        gap: \"24px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: \"var(--accent-color)\",\n                                    marginBottom: \"16px\"\n                                },\n                                children: \"Ready to meet with Exie?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: \"#666\",\n                                    marginBottom: \"32px\"\n                                },\n                                children: \"Join the meeting to start your AI mentoring session\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleJoin,\n                                style: {\n                                    backgroundColor: \"var(--accent-color)\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    padding: \"12px 24px\",\n                                    fontSize: \"16px\",\n                                    fontWeight: \"600\",\n                                    cursor: \"pointer\",\n                                    transition: \"all 0.2s ease\"\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.opacity = \"0.9\",\n                                onMouseOut: (e)=>e.currentTarget.style.opacity = \"1\",\n                                children: \"Join Meeting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoCallGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            joined: joined,\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            exieLoading: exieLoading,\n                            exieVideoUrl: exieVideoUrl,\n                            exieTranscript: exieTranscript,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MeetingControls__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            micOn: micOn,\n                            cameraOn: cameraOn,\n                            onToggleMic: handleToggleMic,\n                            onToggleCamera: handleToggleCamera,\n                            onLeave: handleLeave,\n                            onTalkToExie: handleTalkToExie,\n                            exieLoading: exieLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 254,\n            columnNumber: 8\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, undefined));\n};\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MeetingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@daily-co/daily-react":
/*!****************************************!*\
  !*** external "@daily-co/daily-react" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@daily-co/daily-react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();