/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/meeting";
exports.ids = ["pages/meeting"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/meeting.tsx */ \"./pages/meeting.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/meeting\",\n        pathname: \"/meeting\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_meeting_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/app/globals.css */ \"./src/app/globals.css\");\n/* harmony import */ var _src_app_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_app_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n // Corrected path to global styles\nfunction MyApp({ Component, pageProps }) {\n    // Use the layout defined at the page level, if available\n    const getLayout = Component.getLayout || ((page)=>page);\n    return getLayout(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/_app.tsx\",\n        lineNumber: 18,\n        columnNumber: 20\n    }, this));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0MsQ0FBQyxrQ0FBa0M7QUFhbkUsU0FBU0EsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBc0I7SUFDekQseURBQXlEO0lBQ3pELE1BQU1DLFlBQVlGLFVBQVVFLFNBQVMsSUFBSyxFQUFDQyxPQUFTQSxJQUFHO0lBRXZELE9BQU9ELHdCQUFVLDhEQUFDRjtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUMzQztBQUVBLGlFQUFlRixLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhpZS1haS8uL3BhZ2VzL19hcHAudHN4PzJmYmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zcmMvYXBwL2dsb2JhbHMuY3NzJzsgLy8gQ29ycmVjdGVkIHBhdGggdG8gZ2xvYmFsIHN0eWxlc1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB0eXBlIHsgUmVhY3RFbGVtZW50LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdHlwZSB7IE5leHRQYWdlIH0gZnJvbSAnbmV4dCc7XG5cbmV4cG9ydCB0eXBlIE5leHRQYWdlV2l0aExheW91dCA9IE5leHRQYWdlICYge1xuICBnZXRMYXlvdXQ/OiAocGFnZTogUmVhY3RFbGVtZW50KSA9PiBSZWFjdE5vZGU7XG59O1xuXG5leHBvcnQgdHlwZSBBcHBQcm9wc1dpdGhMYXlvdXQgPSBBcHBQcm9wcyAmIHtcbiAgQ29tcG9uZW50OiBOZXh0UGFnZVdpdGhMYXlvdXQ7XG59O1xuXG5mdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzV2l0aExheW91dCkge1xuICAvLyBVc2UgdGhlIGxheW91dCBkZWZpbmVkIGF0IHRoZSBwYWdlIGxldmVsLCBpZiBhdmFpbGFibGVcbiAgY29uc3QgZ2V0TGF5b3V0ID0gQ29tcG9uZW50LmdldExheW91dCB8fCAoKHBhZ2UpID0+IHBhZ2UpO1xuXG4gIHJldHVybiBnZXRMYXlvdXQoPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPik7XG59XG5cbmV4cG9ydCBkZWZhdWx0IE15QXBwOyAiXSwibmFtZXMiOlsiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJnZXRMYXlvdXQiLCJwYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/meeting.tsx":
/*!***************************!*\
  !*** ./pages/meeting.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_VideoCallGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/VideoCallGrid */ \"./src/components/VideoCallGrid.tsx\");\n/* harmony import */ var _src_components_MeetingControls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/components/MeetingControls */ \"./src/components/MeetingControls.tsx\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @daily-co/daily-react */ \"@daily-co/daily-react\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _src_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/SidebarLayout */ \"./src/components/SidebarLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst MeetingPage = ()=>{\n    // Bring back the state for controls and Exie interaction\n    const [joined, setJoined] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [micOn, setMicOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [cameraOn, setCameraOn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [exieLoading, setExieLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exieVideoUrl, setExieVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Exie's video URL\n    const [exieTranscript, setExieTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Exie's transcript\n    const [roomUrl, setRoomUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Replace Agora client creation with Daily room creation/joining\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // In a real application, you would typically create a room server-side\n        // using your Daily API key and get the room URL from your backend.\n        // For this example, we\\'ll construct a URL directly.\n        const domain = \"YOUR_DAILY_DOMAIN\"; // Replace with your Daily.co domain\n        const roomName = \"my-exie-meeting\"; // Replace with a desired room name\n        const apiKey = \"5bfd26c7ee79f9cc769ad87d9d7825306c3c568d055071a877d74217d4caa3cd\"; // WARNING: Do not expose API key in frontend in production\n        // This is a simplified approach; typically you\\'d handle room creation/joining \n        // with more robust logic, potentially involving tokens for participants.\n        const newRoomUrl = `https://${domain}.daily.co/${roomName}?apiKey=${apiKey}`; // Consider fetching this securely\n        setRoomUrl(newRoomUrl);\n        // Basic check if joined state should be true initially (e.g., if a room URL is set)\n        // This might need refinement based on actual Daily.co connection status\n        if (newRoomUrl) {\n            setJoined(true); // Assume joined if we have a room URL\n        }\n    // Cleanup is handled by DailyProvider automatically on unmount\n    }, []); // Run once on component mount\n    // Handlers for controls - Implement the logic here\n    const handleToggleMic = async ()=>{\n        // Toggle mic logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setMicOn(!micOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the micOn prop.\n    };\n    const handleToggleCamera = async ()=>{\n        // Toggle camera logic - This will need to interact with the track inside VideoCallGrid\n        // For now, we\\'ll just update the state here. VideoCallGrid will use the prop.\n        setCameraOn(!cameraOn);\n    // Actual track enabling/disabling will happen within VideoCallGrid\\'s effects based on the cameraOn prop.\n    };\n    const handleLeave = async ()=>{\n        // Leave logic - This will need to trigger leaving the Agora channel in VideoCallGrid\n        // For now, we\\'ll just update the joined state.\n        setJoined(false);\n        // The actual leave operation will happen within VideoCallGrid based on the joined prop or a specific leave prop.\n        window.location.reload(); // Simple reload to reset state for now\n    };\n    // Handle Talk to Exie logic (re-implemented here)\n    const handleTalkToExie = async ()=>{\n        if (!micOn || exieLoading) return; // Ensure mic state is on and not already loading\n        setExieLoading(true);\n        setExieTranscript(\"\"); // Clear previous transcript\n        setExieVideoUrl(null); // Clear previous video\n        console.log(\"Starting audio recording...\");\n        let mediaRecorder = null;\n        let stream = null;\n        try {\n            // Get a new audio stream specifically for recording\n            stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm\"\n            });\n            const audioChunks = [];\n            mediaRecorder.ondataavailable = (e)=>{\n                if (e.data.size > 0) {\n                    audioChunks.push(e.data);\n                    console.log(\"Collected audio chunk:\", e.data.size, \"bytes\");\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                console.log(\"Audio recording stopped. Total chunks:\", audioChunks.length);\n                const audioBlob = new Blob(audioChunks, {\n                    type: \"audio/webm\"\n                });\n                console.log(\"Audio Blob created:\", audioBlob.size, \"bytes\", audioBlob.type);\n                // Send to /api/transcribe\n                console.log(\"Sending audio to /api/transcribe...\");\n                const res = await fetch(\"/api/transcribe\", {\n                    method: \"POST\",\n                    body: audioBlob,\n                    headers: {\n                        \"Content-Type\": \"audio/webm\"\n                    }\n                });\n                if (!res.ok) throw new Error(`Transcribe API error: ${res.status}`);\n                const data = await res.json();\n                const transcript = data.transcript || \"\";\n                console.log(\"Transcript received:\", transcript);\n                setExieTranscript(transcript); // Update Exie\\'s transcript state\n                if (transcript) {\n                    // Send to AI pipeline (gpt -> voice -> video)\n                    console.log(\"Sending transcript to /api/gpt...\");\n                    const gptRes = await fetch(\"/api/gpt\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            message: transcript,\n                            tweetsOrHandle: \"\"\n                        })\n                    });\n                    if (!gptRes.ok) throw new Error(`GPT API error: ${gptRes.status}`);\n                    const gptData = await gptRes.json();\n                    const mentorReply = gptData.reply || \"Sorry, I could not generate a response.\";\n                    console.log(\"GPT Reply received:\", mentorReply);\n                    setExieTranscript(mentorReply); // Update Exie\\'s transcript state with reply\n                    // ElevenLabs\n                    console.log(\"Sending text to /api/voice...\");\n                    const voiceRes = await fetch(\"/api/voice\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            text: mentorReply\n                        })\n                    });\n                    if (!voiceRes.ok) throw new Error(`Voice API error: ${voiceRes.status}`);\n                    const voiceData = await voiceRes.json();\n                    console.log(\"Voice data received (base64 length):\", voiceData.audioBase64?.length);\n                    if (!voiceData.audioBase64) throw new Error(\"No audio returned from ElevenLabs.\");\n                    // Create a blob URL for the audio\n                    const audioBlob = new Blob([\n                        Uint8Array.from(atob(voiceData.audioBase64), (c)=>c.charCodeAt(0))\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioBlob);\n                    console.log(\"Audio Blob URL created:\", audioUrl);\n                    // D-ID\n                    console.log(\"Sending audio URL to /api/video...\");\n                    const videoRes = await fetch(\"/api/video\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        },\n                        body: JSON.stringify({\n                            audioUrl,\n                            imageUrl: \"https://via.placeholder.com/320x240.png?text=Exie+AI\"\n                        }) // Use consistent placeholder\n                    });\n                    if (!videoRes.ok) throw new Error(`Video API error: ${videoRes.status}`);\n                    const videoData = await videoRes.json();\n                    console.log(\"Video URL received:\", videoData.videoUrl);\n                    if (videoData.videoUrl) setExieVideoUrl(videoData.videoUrl); // Update Exie\\'s video URL state\n                    // Play the audio\n                    const audio = new Audio(audioUrl);\n                    audio.play();\n                } else {\n                    console.log(\"No transcript to process.\");\n                }\n            };\n            mediaRecorder.start();\n            // Stop recording after a short delay (adjust as needed)\n            setTimeout(()=>{\n                if (mediaRecorder?.state !== \"inactive\") mediaRecorder?.stop();\n                // Stop the getUserMedia tracks after recording stops\n                stream?.getTracks().forEach((track)=>track.stop());\n            }, 5000); // Record for 5 seconds max\n        } catch (error) {\n            console.error(\"Error recording audio or processing AI pipeline:\", error);\n            setExieTranscript(\"Sorry, there was an error processing your request.\"); // Update Exie\\'s transcript state on error\n        } finally{\n            setExieLoading(false);\n        }\n    };\n    // Effect to join the Agora channel when component mounts and agoraClient is ready (now handled in VideoCallGrid)\n    // However, we need to set \\'joined\\' state based on connection status.\n    // We\\'ll rely on VideoCallGrid to emit a \\'joined\\' status change or similar if needed, or manage joining entirely in VideoCallGrid\\'s hook.\n    // For simplicity now, assume VideoCallGrid\\'s useJoin hook is sufficient to manage the connection, and we\\'ll rely on the mic/camera tracks being ready to indicate a potential \\'joined\\' state for controls visibility.\n    // A more robust approach might involve a context or callback from VideoCallGrid when truly connected.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simple way to set joined state based on whether local tracks are available\n        if (micOn && cameraOn) {\n        // This is a simplification. A real \\'joined\\' status should come from Agora\\'s connection state.\n        // We will pass the \\'joined\\' prop down, and VideoCallGrid\\'s useJoin will attempt to join.\n        // We might need a mechanism to get the actual connection state back up from VideoCallGrid.\n        // For now, let\\'s rely on VideoCallGrid\\'s internal joined state derived from the Agora client listener\n        // and pass down the mic/camera state and handlers.\n        // The \\'joined\\' state in MeetingPage will primarily control visibility of the footer controls.\n        // Re-add connection state listener if not fully handled by VideoCallGrid\n        // If VideoCallGrid\\'s useJoin manages connection and updates an internal state,\n        // we might need a way for VideoCallGrid to communicate the joined status up.\n        // Let\\'s stick to the current plan: VideoCallGrid handles Agora, MeetingPage handles overall UI and AI pipeline.\n        // MeetingPage\\'s \\'joined\\' state will primarily control visibility of the footer controls.\n        // A potential approach: VideoCallGrid calls a prop function like onJoinedStatusChange(status: boolean)\n        // useEffect(() => {\n        //   if (agoraClient) {\n        //     agoraClient.on(\\'connection-state-change\\', (state) => {\n        //       if (state === \\'CONNECTED\\') {\n        //         setJoined(true);\n        //       } else {\n        //         setJoined(false);\n        //       }\n        //     });\n        //   }\n        // }, [agoraClient]);\n        // For Daily.co, the useRoom hook\\'s state might be a better indicator of joined status.\n        // Let\\'s update the joined state based on the presence of a room URL for now, as done in the initial effect.\n        }\n    }, [\n        micOn,\n        cameraOn\n    ]); // Depend on micOn and cameraOn to re-check joined status (simplified)\n    // Render the UI only if roomUrl is available (meaning we are attempting to join or are in a room)\n    if (!roomUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading meeting...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 224,\n            columnNumber: 12\n        }, undefined); // Or a proper loading spinner/page\n    }\n    return(// Wrap the meeting content in DailyProvider to provide the Daily room context to children\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_4__.DailyProvider, {\n        url: roomUrl,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                height: \"100vh\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"#f8f8f8\"\n            },\n            children: [\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_VideoCallGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    joined: joined,\n                    micOn: micOn,\n                    cameraOn: cameraOn,\n                    exieLoading: exieLoading,\n                    exieVideoUrl: exieVideoUrl,\n                    exieTranscript: exieTranscript,\n                    onToggleMic: handleToggleMic,\n                    onToggleCamera: handleToggleCamera,\n                    onLeave: handleLeave,\n                    onTalkToExie: handleTalkToExie\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, undefined),\n                joined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_MeetingControls__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    micOn: micOn,\n                    cameraOn: cameraOn,\n                    onToggleMic: handleToggleMic,\n                    onToggleCamera: handleToggleCamera,\n                    onLeave: handleLeave,\n                    onTalkToExie: handleTalkToExie,\n                    exieLoading: exieLoading\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n            lineNumber: 233,\n            columnNumber: 8\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined));\n};\nMeetingPage.getLayout = function getLayout(page) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_SidebarLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: page\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/pages/meeting.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MeetingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/meeting.tsx\n");

/***/ }),

/***/ "./src/components/MeetingControls.tsx":
/*!********************************************!*\
  !*** ./src/components/MeetingControls.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst MeetingControls = ({ micOn, cameraOn, onToggleMic, onToggleCamera, onLeave, onTalkToExie, exieLoading })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            gap: \"16px\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            padding: \"16px 24px\",\n            backgroundColor: \"#fff\",\n            borderRadius: \"8px\",\n            boxShadow: \"0 4px 8px rgba(0,0,0,0.05)\",\n            margin: \"16px 0\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggleMic,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: micOn ? \"#0070f3\" : \"#ccc\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: micOn ? \"\\uD83C\\uDFA4\" : \"\\uD83D\\uDD07\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/MeetingControls.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggleCamera,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: cameraOn ? \"#0070f3\" : \"#ccc\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: cameraOn ? \"\\uD83D\\uDCF7\" : \"\\uD83D\\uDEAB\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/MeetingControls.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onTalkToExie,\n                disabled: exieLoading,\n                style: {\n                    backgroundColor: exieLoading ? \"#ccc\" : \"var(--accent-color)\",\n                    color: \"#fff\",\n                    padding: \"12px 24px\",\n                    borderRadius: \"4px\",\n                    border: \"none\",\n                    cursor: exieLoading ? \"not-allowed\" : \"pointer\",\n                    fontSize: \"16px\",\n                    fontWeight: 600\n                },\n                children: exieLoading ? \"Exie is thinking...\" : \"Talk to Exie\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/MeetingControls.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onLeave,\n                style: {\n                    padding: 12,\n                    borderRadius: \"50%\",\n                    background: \"#e74c3c\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    fontSize: 20\n                },\n                children: \"\\uD83D\\uDEAA\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/MeetingControls.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/MeetingControls.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MeetingControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MeetingControls.tsx\n");

/***/ }),

/***/ "./src/components/SidebarLayout.tsx":
/*!******************************************!*\
  !*** ./src/components/SidebarLayout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst SidebarLayout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                style: {\n                    width: \"200px\",\n                    backgroundColor: \"#f0f0f0\",\n                    padding: \"16px\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            listStyle: \"none\",\n                            padding: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/tweet-center\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Tweet Center\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                style: {\n                                    marginBottom: \"16px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/meeting\",\n                                    style: {\n                                        color: \"var(--accent-color)\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"AI Meeting\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    flexGrow: 1,\n                    padding: \"24px\"\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/SidebarLayout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/SidebarLayout.tsx\n");

/***/ }),

/***/ "./src/components/VideoCallGrid.tsx":
/*!******************************************!*\
  !*** ./src/components/VideoCallGrid.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @daily-co/daily-react */ \"@daily-co/daily-react\");\n/* harmony import */ var _daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Using a more neutral placeholder for a white theme\nconst EXIE_VIDEO_PLACEHOLDER = \"https://via.placeholder.com/320x240.png?text=Exie+AI\";\n// Update component signature to accept props\nconst VideoCallGrid = ({ joined, micOn, cameraOn, exieLoading, exieVideoUrl, exieTranscript, onToggleMic, onToggleCamera, onLeave, onTalkToExie })=>{\n    const room = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useRoom)();\n    const localParticipant = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useLocalParticipant)();\n    // Get IDs of all participants, excluding the local participant\n    const participantIds = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useParticipantIds)();\n    const remoteParticipantIds = participantIds.filter((id)=>id !== localParticipant?.session_id);\n    // Handlers for controls - these just call the parent-provided handlers\n    // These are not directly used in this component's render but are available if needed.\n    // const handleToggleMicClick = () => { onToggleMic(); };\n    // const handleToggleCameraClick = () => { onToggleCamera(); };\n    // const handleLeaveClick = async () => { onLeave(); };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"grid\",\n            gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n            gap: 16,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            padding: 16,\n            backgroundColor: \"#ffffff\",\n            borderRadius: 8,\n            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n            flexGrow: 1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    border: \"1px solid rgba(0, 123, 255, 0.3)\",\n                    borderRadius: 8,\n                    overflow: \"hidden\",\n                    width: \"100%\",\n                    paddingTop: \"75%\",\n                    position: \"relative\",\n                    background: \"#eee\",\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    exieLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: \"#555\",\n                            textAlign: \"center\",\n                            margin: \"auto\"\n                        },\n                        children: \"Exie is thinking...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined) // Darker text\n                     : exieVideoUrl ? // Use controls only for debugging, remove for final version\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        src: exieVideoUrl,\n                        autoPlay: true,\n                        playsInline: true,\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: EXIE_VIDEO_PLACEHOLDER,\n                        alt: \"Exie AI\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            textAlign: \"center\",\n                            background: \"rgba(255,255,255,0.7)\",\n                            color: \"#333\",\n                            fontWeight: 600,\n                            padding: \"4px 0\"\n                        },\n                        children: \"Exie (AI)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    exieTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"#fff\",\n                            padding: 8,\n                            fontSize: 14,\n                            maxHeight: \"50%\",\n                            overflowY: \"auto\"\n                        },\n                        children: exieTranscript\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, \"exie-ai\", true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            localParticipant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    border: \"1px solid rgba(0, 123, 255, 0.5)\",\n                    borderRadius: 8,\n                    overflow: \"hidden\",\n                    width: \"100%\",\n                    paddingTop: \"75%\",\n                    position: \"relative\",\n                    background: \"#eee\"\n                },\n                children: [\n                    localParticipant.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyVideo, {\n                        sessionId: localParticipant.session_id,\n                        type: \"video\",\n                        autoPlay: true,\n                        // The DailyVideo component handles attaching the track internally\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            width: \"100%\",\n                            height: \"100%\",\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            color: \"#555\"\n                        },\n                        children: \"Camera Off\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            textAlign: \"center\",\n                            background: \"rgba(255,255,255,0.7)\",\n                            color: \"var(--accent-color)\",\n                            fontWeight: 600,\n                            padding: \"4px 0\"\n                        },\n                        children: \"You\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, localParticipant.session_id, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined),\n            remoteParticipantIds.map((id)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RemoteParticipantVideo, {\n                    participantId: id\n                }, id, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, undefined);\n            })\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n// Helper component to render remote participant video\nconst RemoteParticipantVideo = ({ participantId })=>{\n    const participant = (0,_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.useParticipant)(participantId);\n    // Use DailyVideo component for remote participants\n    if (!participant || !participant.video) {\n        return null; // Explicitly return null if no video to render or if track is boolean (e.g., false)\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            border: \"1px solid rgba(0, 123, 255, 0.3)\",\n            borderRadius: 8,\n            overflow: \"hidden\",\n            width: \"100%\",\n            paddingTop: \"75%\",\n            position: \"relative\",\n            background: \"#eee\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_co_daily_react__WEBPACK_IMPORTED_MODULE_2__.DailyVideo, {\n                sessionId: participant.session_id,\n                type: \"video\",\n                autoPlay: true,\n                style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    height: \"100%\",\n                    objectFit: \"cover\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    bottom: 0,\n                    left: 0,\n                    right: 0,\n                    textAlign: \"center\",\n                    background: \"rgba(255,255,255,0.7)\",\n                    color: \"#333\",\n                    fontWeight: 600,\n                    padding: \"4px 0\"\n                },\n                children: [\n                    \"User \",\n                    participant.user_name || participant.session_id.substring(0, 8)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            \" \"\n        ]\n    }, participant.session_id, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/Exie/src/components/VideoCallGrid.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoCallGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/VideoCallGrid.tsx\n");

/***/ }),

/***/ "./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (() => {



/***/ }),

/***/ "@daily-co/daily-react":
/*!****************************************!*\
  !*** external "@daily-co/daily-react" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@daily-co/daily-react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fmeeting&preferredRegion=&absolutePagePath=.%2Fpages%2Fmeeting.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();