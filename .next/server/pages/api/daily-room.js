"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/daily-room";
exports.ids = ["pages/api/daily-room"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdaily-room&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fdaily-room.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdaily-room&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fdaily-room.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_daily_room_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/daily-room.ts */ \"(api)/./pages/api/daily-room.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_daily_room_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_daily_room_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/daily-room\",\n        pathname: \"/api/daily-room\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_daily_room_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmRhaWx5LXJvb20mcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRmRhaWx5LXJvb20udHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDc0Q7QUFDdEQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHFEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxxREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leGllLWFpLz8zMDE0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvZGFpbHktcm9vbS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2RhaWx5LXJvb21cIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9kYWlseS1yb29tXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdaily-room&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fdaily-room.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/daily-room.ts":
/*!*********************************!*\
  !*** ./pages/api/daily-room.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { roomName } = req.body;\n    const apiKey = process.env.DAILY_API_KEY;\n    if (!roomName) {\n        return res.status(400).json({\n            error: \"Room name is required\"\n        });\n    }\n    try {\n        if (!apiKey) {\n            // Return a working public Daily.co room for testing\n            console.log(\"No Daily.co API key found, returning public demo room URL\");\n            // Use Daily.co's public demo room that actually works\n            const demoRoomUrl = \"https://demo.daily.co/hello\";\n            return res.status(200).json({\n                url: demoRoomUrl\n            });\n        }\n        // Create a room using Daily.co REST API\n        const response = await fetch(\"https://api.daily.co/v1/rooms\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                name: roomName,\n                properties: {\n                    max_participants: 10,\n                    enable_chat: true,\n                    enable_screenshare: true,\n                    enable_recording: false,\n                    start_video_off: false,\n                    start_audio_off: false,\n                    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Daily.co API error: ${response.status} - ${errorData.error || \"Unknown error\"}`);\n        }\n        const data = await response.json();\n        return res.status(200).json({\n            url: data.url\n        });\n    } catch (error) {\n        console.error(\"Error creating Daily.co room:\", error);\n        return res.status(500).json({\n            error: \"Error creating room.\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/daily-room.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdaily-room&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fdaily-room.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();